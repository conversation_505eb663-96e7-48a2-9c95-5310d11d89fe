import 'dart:async';
import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter_app_kouyu/common/http/request_error.dart';
import 'package:flutter_app_kouyu/common/utils/log.dart';
import 'package:flutter_app_kouyu/common/utils/package_util.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

import 'package:flutter_app_kouyu/common/config/env.dart';
import 'package:flutter_app_kouyu/common/utils/login_util.dart';
import 'package:flutter_app_kouyu/my_app.dart';
import 'package:flutter_app_kouyu/widgets/overlay_manager.dart';
import 'package:flutter_app_kouyu/widgets/trial_expires_widget.dart';

class Request {
  // 配置 Dio 实例
  static final BaseOptions _options = BaseOptions(
    baseUrl: EnvConfig.baseUrl(),
    headers: {
      "appKey": "0003",
      "iosAppVersion": PackageUtil.packageInfo.version,
      "ppVersion": PackageUtil.packageInfo.version,
    },
    contentType: "application/json",
    connectTimeout: const Duration(seconds: 10),
    receiveTimeout: const Duration(seconds: 60),
  );

  // 创建 Dio 实例
  static final Dio _dio = Dio(_options);

  static final BaseOptions _streamOptions = BaseOptions(
    baseUrl: EnvConfig.baseUrl(),
    headers: {
      "appKey": "0003",
      "iosAppVersion": PackageUtil.packageInfo.version,
      "appVersion": PackageUtil.packageInfo.version,
    },
    contentType: "application/json",
    connectTimeout: const Duration(seconds: 10),
    receiveTimeout: const Duration(seconds: 20),
    responseType: ResponseType.stream,
  );
  static final Dio _streamDio = Dio(_streamOptions);

  // _request 是核心函数，所有的请求都会走这里
  static Future<T> _request<T>(String path,
      {required String method,
      Map<String, dynamic>? queryParameters,
      Options? options,
      bool hideError = false,
      data}) async {
    //添加token
    _dio.options.headers["sessionId"] =
        LoginUtil.loginInfoModel()?.data?.accessToken ?? "";

    Log.d('API发送的接口为：$path');
    Log.d('API发送的参数为：$data');
    Log.d('API发送的queryParameters为：$queryParameters');

    try {
      if (options == null) {
        options = Options(method: method);
      } else {
        options = options.copyWith(method: method);
      }
      Response response = await _dio.request(path,
          queryParameters: queryParameters, data: data, options: options);
      if (response.statusCode == 200 || response.statusCode == 201) {
        try {
          if (response.statusCode != 200) {
            Log.e("API服务器错误，状态码为：${response.statusCode}");
            if (!hideError) {
              EasyLoading.showInfo('服务器错误，状态码为：${response.statusCode}');
            }
            return Future.error(RequestError(
                code: "${response.statusCode}",
                message: response.statusMessage));
          }

          Log.d('API响应的数据为：${response.data.toString()},path:$path');
          if (response.data["code"] == "0") {
            Log.e('API接口报错：path:$path');
            String? errorCode = response.data["error_code"];
            if (errorCode != null) {
              if (errorCode == "0004") {
                //token失效
                globalNavigatorKey.currentState
                    ?.pushNamedAndRemoveUntil("/login", (route) => false);
              } else if (errorCode == "0023") {
                //体验到期
                OverlayManager.getInstance().showOverlay(
                  true,
                  builder: (close) => TrialExpiresWidget(
                    close: close,
                  ),
                );

                return Future.error(RequestError(
                    code: errorCode, message: response.data["error_msg"]));
              }
            }
            if (!hideError) {
              EasyLoading.showError(response.data["error_msg"]);
            }
            return Future.error(RequestError(
                code: errorCode, message: response.data["error_msg"]));
          }

          if (response.data is Map) {
            return response.data;
          } else {
            return json.decode(response.data.toString());
          }
        } catch (e) {
          Log.e("API解析响应数据异常:$e");
          if (!hideError) {
            EasyLoading.showInfo('解析响应数据异常');
          }
          return Future.error(RequestError(message: '解析响应数据异常'));
        }
      } else {
        Log.e("APIHTTP错误，状态码为：${response.statusCode}");
        if (!hideError) {
          EasyLoading.showInfo('HTTP错误，状态码为：${response.statusCode}');
        }
        String message = _handleHttpError(response.statusCode ?? 999);
        if (!hideError) {
          EasyLoading.showError(message);
        }
        return Future.error(RequestError(message: 'HTTP错误'));
      }
    } on DioException catch (e, _) {
      Log.e("API请求异常:${_dioError(e)},path:$path");
      if (!hideError) {
        EasyLoading.showInfo(_dioError(e));
      }
      return Future.error(RequestError(message: _dioError(e)));
    } catch (e, _) {
      Log.e("API未知异常:$e");
      return Future.error(RequestError(message: '未知异常'));
    }
  }

  // 处理 Dio 异常
  static String _dioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return "网络连接超时，请检查网络设置";
      case DioExceptionType.receiveTimeout:
        return "服务器超时，请稍后重试！";
      case DioExceptionType.sendTimeout:
        return "网络连接超时，请检查网络设置";
      case DioExceptionType.badResponse:
        return "服务器异常，请稍后重试！";
      case DioExceptionType.cancel:
        return "请求已被取消，请重新请求";
      case DioExceptionType.unknown:
        return "网络异常，请稍后重试！";
      default:
        return "系统异常";
    }
  }

  // 处理 Http 错误码
  static String _handleHttpError(int errorCode) {
    String message;
    switch (errorCode) {
      case 400:
        message = '请求语法错误';
        break;
      case 401:
        message = '未授权，请登录';
        break;
      case 403:
        message = '拒绝访问';
        break;
      case 404:
        message = '请求出错';
        break;
      case 408:
        message = '请求超时';
        break;
      case 500:
        message = '服务器异常';
        break;
      case 501:
        message = '服务未实现';
        break;
      case 502:
        message = '网关错误';
        break;
      case 503:
        message = '服务不可用';
        break;
      case 504:
        message = '网关超时';
        break;
      case 505:
        message = 'HTTP版本不受支持';
        break;
      default:
        message = '请求失败，错误码：$errorCode';
    }
    return message;
  }

  static Future<T> get<T>(String path,
      {Map<String, dynamic>? params,
      bool hideError = false,
      Options? options}) {
    return _request(path,
        method: 'get',
        queryParameters: params,
        hideError: hideError,
        options: options);
  }

  static Future<T> post<T>(String path,
      {Map<String, dynamic>? params,
      data,
      bool hideError = false,
      Options? options}) {
    return _request<T>(path,
        method: 'post',
        queryParameters: params,
        options: options,
        data: data,
        hideError: hideError);
  }

  static Future<T> streamPost<T>(String path,
      {Map<String, dynamic>? params,
      data,
      bool hideError = false,
      Options? options}) {
    return _streamRequest<T>(path,
        method: 'post',
        queryParameters: params,
        options: options,
        data: data,
        hideError: hideError);
  }

  static Future<T> _streamRequest<T>(String path,
      {required String method,
      Map<String, dynamic>? queryParameters,
      Options? options,
      bool hideError = false,
      data}) async {
    //添加token
    _streamDio.options.headers["sessionId"] =
        LoginUtil.loginInfoModel()?.data?.accessToken ?? "";

    Log.d('API发送的接口为：$path');
    Log.d('API发送的参数为：$data');
    Log.d('API发送的queryParameters为：$queryParameters');

    try {
      if (options == null) {
        options = Options(method: method);
      } else {
        options = options.copyWith(method: method);
      }
      Response response = await _streamDio.request(path,
          queryParameters: queryParameters, data: data, options: options);
      if (response.statusCode == 200 || response.statusCode == 201) {
        try {
          if (response.statusCode != 200) {
            Log.e("API服务器错误，状态码为：${response.statusCode}");
            if (!hideError) {
              EasyLoading.showInfo('服务器错误，状态码为：${response.statusCode}');
            }
            return Future.error(RequestError(
                code: "${response.statusCode}",
                message: response.statusMessage));
          }

          Log.d('API响应的数据为：${response.data.toString()},path:$path');
          // processStreamResponse(response.data.stream);
          return response.data.stream;
          //  return processStreamResponse(response.data.stream);
          //       return response.data.stream.transform(StreamTransformer.fromHandlers(
          //    handleData: (data, sink){
          //     try {
          //       sink.add(Utf8Decoder().convert(data));
          //     } catch (e) {
          //       sink.addError(e);
          //     }
          //    }
          // ));
        } catch (e) {
          Log.e("API解析响应数据异常:$e");
          if (!hideError) {
            EasyLoading.showInfo('解析响应数据异常');
          }
          return Future.error(RequestError(message: '解析响应数据异常'));
        }
      } else {
        Log.e("APIHTTP错误，状态码为：${response.statusCode}");
        if (!hideError) {
          EasyLoading.showInfo('HTTP错误，状态码为：${response.statusCode}');
        }
        String message = _handleHttpError(response.statusCode ?? 999);
        if (!hideError) {
          EasyLoading.showError(message);
        }
        return Future.error(RequestError(message: 'HTTP错误'));
      }
    } on DioException catch (e, _) {
      Log.e("API请求异常:${_dioError(e)},path:$path");
      if (!hideError) {
        EasyLoading.showInfo(_dioError(e));
      }
      return Future.error(RequestError(message: _dioError(e)));
    } catch (e, _) {
      Log.e("API未知异常:$e");
      return Future.error(RequestError(message: '未知异常'));
    }
  }

  static void processStreamResponse(Stream stream) async {
    Log.d('Processing stream response');
// 用于每个阶段的对话结果
    final StringBuffer buffer = StringBuffer();

    // 处理流式响应
    await for (var data in stream) {
      final bytes = data as List<int>;
      final decodedData = utf8.decode(bytes);
      print(decodedData);
    }
  }
}
