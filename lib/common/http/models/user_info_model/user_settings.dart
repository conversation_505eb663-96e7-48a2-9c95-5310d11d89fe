import 'dart:convert';

class UserSettings {
  num? characterAge;
  List<String>? characterAgeGroup;
  String? characterAudioUrl;
  String? iconUrl;
  String? chatCharacterUrl;
  String? characterConversationVideoUrl;
  String? characterEnglishVariation;
  String? characterMbti;
  String? homePageImageUrl;
  num? characterGender;
  String? characterGreeting;
  String? characterGreetingCn;
  String? characterGreetingVideoUrl;
  String? characterImageUrl;
  String? characterName;
  String? characterSelfIntroduction;
  String? characterStaticVideoUrl;
  String? characterType;
  String? characterVoice;
  num? isVideoOpen;
  num? playbackSpeed;
  num? userId;
  List<String>? learningPurpose;
  String? learningPhase;
  num? isMixedTeaching;

  UserSettings({
    this.characterAge,
    this.characterAgeGroup,
    this.characterAudioUrl,
    this.iconUrl,
    this.chatCharacterUrl,
    this.characterConversationVideoUrl,
    this.characterEnglishVariation,
    this.characterMbti,
    this.homePageImageUrl,
    this.characterGender,
    this.characterGreeting,
    this.characterGreetingCn,
    this.characterGreetingVideoUrl,
    this.characterImageUrl,
    this.characterName,
    this.characterSelfIntroduction,
    this.characterStaticVideoUrl,
    this.characterType,
    this.characterVoice,
    this.isVideoOpen,
    this.playbackSpeed,
    this.userId,
    this.learningPurpose,
    this.learningPhase,
    this.isMixedTeaching,
  });

  @override
  String toString() {
    return 'UserSettings(characterAge: $characterAge, characterAgeGroup: $characterAgeGroup, characterAudioUrl: $characterAudioUrl,iconUrl: $iconUrl,chatCharacterUrl: $chatCharacterUrl, characterConversationVideoUrl: $characterConversationVideoUrl, characterEnglishVariation: $characterEnglishVariation,characterMbti: $characterMbti, characterGender: $characterGender, characterGreeting: $characterGreeting, characterGreetingCn: $characterGreetingCn, characterGreetingVideoUrl: $characterGreetingVideoUrl, characterImageUrl: $characterImageUrl, characterName: $characterName, characterSelfIntroduction: $characterSelfIntroduction, characterStaticVideoUrl: $characterStaticVideoUrl, characterType: $characterType, characterVoice: $characterVoice, isVideoOpen: $isVideoOpen, playbackSpeed: $playbackSpeed, userId: $userId)';
  }

  factory UserSettings.fromMap(Map<String, dynamic> data) => UserSettings(
        characterAge: data['character_age'] as num?,
        characterAgeGroup: (data['character_age_group'] as List?)?.map((e) => e.toString()).toList(),
        characterAudioUrl: data['character_audio_url'] as String?,
        iconUrl: data['icon_url'] as String?,
        chatCharacterUrl: data['chat_character_url'] as String?,
        characterConversationVideoUrl:
            data['character_conversation_video_url'] as String?,
        characterEnglishVariation:
            data['character_english_variation'] as String?,
        characterMbti: data['character_mbti'] as String?,
        homePageImageUrl: data['home_page_bg_image_url'] as String?,
        characterGender: data['character_gender'] as num?,
        characterGreeting: data['character_greeting'] as String?,
        characterGreetingCn: data['character_greeting_cn'] as String?,
        characterGreetingVideoUrl:
            data['character_greeting_video_url'] as String?,
        characterImageUrl: data['character_image_url'] as String?,
        characterName: data['character_name'] as String?,
        characterSelfIntroduction:
            data['character_self_introduction'] as String?,
        characterStaticVideoUrl: data['character_static_video_url'] as String?,
        characterType: data['character_type'] as String?,
        characterVoice: data['character_voice'] as String?,
        isVideoOpen: data['is_video_open'] as num?,
        playbackSpeed: data['playback_speed'] as num?,
        userId: data['user_id'] as num?,
        learningPurpose: (data['learning_purpose'] as List?)?.map((e) => e.toString()).toList(),
        learningPhase: data['learning_phase'] as String?,
        isMixedTeaching: data['is_mixed_teaching'] as num?,
      );

  Map<String, dynamic> toMap() => {
        'character_age': characterAge,
        'character_age_group': characterAgeGroup,
        'character_audio_url': characterAudioUrl,
        'icon_url': iconUrl,
        'chat_character_url': chatCharacterUrl,
        'character_conversation_video_url': characterConversationVideoUrl,
        'character_english_variation': characterEnglishVariation,
        'character_mbti': characterMbti,
        'home_page_bg_image_url': homePageImageUrl,
        'character_gender': characterGender,
        'character_greeting': characterGreeting,
        'character_greeting_cn': characterGreetingCn,
        'character_greeting_video_url': characterGreetingVideoUrl,
        'character_image_url': characterImageUrl,
        'character_name': characterName,
        'character_self_introduction': characterSelfIntroduction,
        'character_static_video_url': characterStaticVideoUrl,
        'character_type': characterType,
        'character_voice': characterVoice,
        'is_video_open': isVideoOpen,
        'playback_speed': playbackSpeed,
        'user_id': userId,
        'learning_purpose': learningPurpose,
        'learning_phase': learningPhase,
        'is_mixed_teaching': isMixedTeaching,
      };

  /// `dart:convert`
  ///
  /// Parses the string and returns the resulting Json object as [UserSettings].
  factory UserSettings.fromJson(String data) {
    return UserSettings.fromMap(json.decode(data) as Map<String, dynamic>);
  }

  /// `dart:convert`
  ///
  /// Converts [UserSettings] to a JSON string.
  String toJson() => json.encode(toMap());
}
