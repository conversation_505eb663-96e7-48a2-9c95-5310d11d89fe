import 'package:flutter_app_kouyu/common/database/base_model.dart';
import 'package:flutter_app_kouyu/common/database/model/chat.dart';
import 'package:flutter_app_kouyu/common/config/env.dart';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';

class DatabaseHelper<T extends BaseModel> {
  DatabaseHelper._(); //单例模式

  static final DatabaseHelper db = DatabaseHelper._();
  static Database? _database;
  static const int _version = 1;

  Future<Database?> get database async {
    _database ??= await initDb();
    return _database;
  }

  //init database and open it
  Future<Database> initDb() async {
    String path = join(await getDatabasesPath(), EnvConfig.dbName());

    Database db =
        await openDatabase(path, version: _version, onOpen: (db) async {
      // 等待表创建完成
      await db.execute(
        //创建自由对话表
        'CREATE TABLE chat(id INTEGER PRIMARY KEY,userId INTEGER NOT NULL,chatRoomId INTEGER NOT NULL,type INTEGER,content TEXT,audio_url TEXT,translateText TEXT,createTime INTEGER,updateTime INTEGER)',
      );
    });
    return db;
  }

  //insert database
  Future<int?> insert(T t) async {
    final db = await database; // 确保database在上下文中定义

    try {
      Map<String, dynamic> map = t.toMap();
      var result = await db?.insert("chat", map);
      return result;
    } on DatabaseException catch (_) {
      return -1;
    }
  }

  //query database
  Future<List<T>> getAll() async {
    var db = await database;
    var query = await db?.query('chat', orderBy: 'id DESC');

    List<T> tasks = query!.isNotEmpty
        ? query.map((t) {
            if (t is Chat) {
              return Chat.fromMap(t) as T;
            }
            throw Exception("covert exception");
          }).toList()
        : [];
    return tasks;
  }

  //delete sql by id
  // Future<void> delete(int id) async {
  //   var db = await database;
  //   await db?.rawDelete('DELETE FROM timerdata WHERE id = ?', [id]);
  // }

  // //delete sql by title
  // Future<void> deleteByTitle(String title) async {
  //   var db = await database;
  //   await db?.rawDelete('DELETE FROM timerdata WHERE title = ?', [title]);
  // }

  //update database by id
  Future<void> updateDatabase(T t) async {
    final db = await database;
    await db?.update(
      'timerdata',
      t.toMap(),
      where: "id = ?",
      whereArgs: [t.id],
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  //update database by title
  // Future<void> updateDatabaseByTitle(Chat chat) async {
  //   final db = await database;
  //   await db?.update(
  //     'timerdata',
  //     chat.toMap(),
  //     where: "id = ?",
  //     whereArgs: [chat.id],
  //     conflictAlgorithm: ConflictAlgorithm.replace,
  //   );
  // }
}
