// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:flutter_app_kouyu/common/database/base_model.dart';

class Chat extends BaseModel {
  final int? userId;
  //自由对话id，后续支持多个对话使用，默认为1
  final int? chatRoomId;
  //1 question  2 answer
  final int? type;
  //对话内容
  final String? content;
  //对话语音url
  final String? audioUrl;
  //翻译内容
  final String? translateText;
  final int? createTime;
  final int? updateTime;

  Chat(
      {required super.id,
      required this.userId,
      this.chatRoomId = 1,
      required this.type,
      required this.content,
      required this.audioUrl,
      required this.translateText,
      required this.createTime,
      required this.updateTime});

  @override
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'userId': userId,
      'chatRoomId': chatRoomId,
      'type': type,
      'content': content,
      'audioUrl': audioUrl,
      'translateText': translateText,
      'createTime': createTime,
      'updateTime': updateTime,
    };
  }

  @override
  String toString() {
    return 'Chat(id: $id, userId: $userId, chatRoomId: $chatRoomId, type: $type, content: $content, audioUrl: $audioUrl, translateText: $translateText, createTime: $createTime, updateTime: $updateTime)';
  }

  factory Chat.fromMap(Map<String, dynamic> map) {
    return Chat(
      id: map['id'] != null ? map['id'] as int : null,
      userId: map['userId'] ? map['userId'] as int : null,
      chatRoomId: map['chatRoomId'] != null ? map['chatRoomId'] as int : null,
      type: map['type'] != null ? map['type'] as int : null,
      content: map['content'] != null ? map['content'] as String : null,
      audioUrl: map['audioUrl'] != null ? map['audioUrl'] as String : null,
      translateText:
          map['translateText'] != null ? map['translateText'] as String : null,
      createTime: map['createTime'] != null ? map['createTime'] as int : null,
      updateTime: map['updateTime'] != null ? map['updateTime'] as int : null,
    );
  }

  String toJson() => json.encode(toMap());

  factory Chat.fromJson(String source) =>
      Chat.fromMap(json.decode(source) as Map<String, dynamic>);
}
