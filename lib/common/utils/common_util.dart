import 'dart:async';

import 'package:audio_session/audio_session.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/utils/log.dart';
import 'package:flutter_app_kouyu/common/utils/package_util.dart';
import 'package:flutter_vlc_player/flutter_vlc_player.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_vlc_player/src/vlc_player_platform.dart';
import 'package:wechat_kit/wechat_kit.dart';

enum PlayState {
  /// The player has not loaded an [AudioSource].
  idle,

  /// The player is loading an [AudioSource].
  loading,

  /// The player is buffering audio and unable to play.
  buffering,

  /// The player is has enough audio buffered and is able to play.
  ready,

  /// The player has reached the end of the audio.
  completed,
}

class CommonUtils {
  static VlcPlayerController videoPlayerController =
      VlcPlayerController.network(
    "",
    hwAcc: HwAcc.full,
    autoPlay: false,
    allowBackgroundPlayback: true,
    options: VlcPlayerOptions(),
  );
  static final _streamcontroller = StreamController<PlayState>.broadcast();
  static String? _url;
  static String? _nextUrl;

  //播放的唯一key，用来UI监听播放结束
  static String? _audio_key;
  static late SharedPreferences sharedPreferences;
  static void Function()? finishBlock;
  static Future<bool> init() async {
    sharedPreferences = await SharedPreferences.getInstance();
    PackageUtil.packageInfo = await PackageInfo.fromPlatform();

    videoPlayerController.addOnInitListener(() {
      Log.d("videoPlayerController init");
    });
    videoPlayerController.addListener(() {
      Log.d("playingState: ${videoPlayerController.value.playingState}");

      switch (videoPlayerController.value.playingState) {
        case PlayingState.initializing:
          _streamcontroller.add(PlayState.idle);
          return;
        case PlayingState.initialized:
          _streamcontroller.add(PlayState.idle);
          return;
        case PlayingState.buffering:
          _streamcontroller.add(PlayState.buffering);
          return;
        case PlayingState.playing:
          _streamcontroller.add(PlayState.ready);
          return;
        case PlayingState.stopped:
          _streamcontroller.add(PlayState.completed);
          return;
        case PlayingState.ended:
          _streamcontroller.add(PlayState.completed);
          if (_nextUrl != null) {
            playVideo(_nextUrl);
            _nextUrl = null;
          }
          return;
        case PlayingState.error:
          _streamcontroller.add(PlayState.completed);
          return;
        default:
          _streamcontroller.add(PlayState.completed);
      }
    });

    return true;
  }

  static bool get isPlaying {
    return videoPlayerController.value.playingState == PlayingState.playing;
  }

  static bool get isBuffering {
    return videoPlayerController.value.playingState == PlayingState.buffering;
  }

  static Stream<PlayState> get playStatus {
    return _streamcontroller.stream;
  }

  static String? get url {
    return _url;
  }

  static String? get key {
    return _audio_key;
  }

  static playVideo(String? url,
      {String? nextUrl, String? key, void Function()? finish}) async {
    if (url == null) {
      return;
    }
    // await stopPlay();
    Log.d("playing: $url");
    _url = url;
    _nextUrl = nextUrl;
    _audio_key = key;
    final audioSession = await AudioSession.instance;
    await audioSession.configure(const AudioSessionConfiguration.music());
    await audioSession.setActive(true);
    await videoPlayerController.setMediaFromNetwork(url, autoPlay: false);
    videoPlayerController.play();
  }

  static stopPlay() async {
    try {
      _nextUrl = null;
      await videoPlayerController.stop();
    } catch (_) {}
  }

  static pause() async {
    try {
      await videoPlayerController.pause();
    } catch (_) {}
  }

  static playTts(String? text, {String? key}) async {
    if (text == null || text.isEmpty) {
      return;
    }
    Api.tts(text).then((value) {
      playVideo(value, key: key);
    });
  }
}
