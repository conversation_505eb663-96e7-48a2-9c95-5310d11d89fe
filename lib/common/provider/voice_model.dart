// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:async';

import 'package:asr_plugin/asr_plugin.dart';
import 'package:common_utils/common_utils.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:rxdart/rxdart.dart';

import 'package:flutter_app_kouyu/common/asr/asr_util.dart';
import 'package:flutter_app_kouyu/common/utils/common_util.dart';
import 'package:flutter_app_kouyu/common/utils/permission_dialog_util.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/voice_cubit.dart';

class VoiceModel extends ChangeNotifier {
  VoiceStateEnum voiceState = VoiceStateEnum.init;
  ValueStream<bool> get realFinish => _realFinish.stream;
  String get filePath => _config.audio_file_path;
  ASRController? _controller;
  Timer? _timer;
  final _maxSeconds = 60.0;
  int _seconds = 0;
  BehaviorSubject<bool> _realFinish = BehaviorSubject<bool>.seeded(false);
  String result = "";
  List<String> _sentences = [];
  late ASRControllerConfig _config;
  BuildContext buildContext;
  VoiceModel({
    required this.buildContext,
  });

  /// 英文识别
  /// [cb] 识别结果回调, 参数是识别结果
  void speakEn([Function(String result)? cb]) async {
    //停止播放声音
    CommonUtils.stopPlay();
    bool access = await _initRecorder();
    if (!access) {
      return;
    }
    startProgress();
    startRecord(engineModelType: "16k_en", cb: cb);
    voiceState = VoiceStateEnum.recording;
    notifyListeners();
  }

  /// 中文识别
  /// [cb] 识别结果回调, 参数是识别结果
  void speakZh([Function(String result)? cb]) async {
    //停止播放声音
    CommonUtils.stopPlay();
    bool access = await _initRecorder();
    if (!access) {
      return;
    }
    startProgress();
    startRecord(engineModelType: "16k_zh", cb: cb);
    voiceState = VoiceStateEnum.recording;
    notifyListeners();
  }

  void startProgress() {
    _timer = Timer.periodic(const Duration(seconds: 1), (_) {
      _seconds = _seconds + 1;
      if (_seconds.toDouble() > _maxSeconds) {
        _timer?.cancel();
      }
    });
  }

  @mustCallSuper
  Future<bool> stop() async {
    _timer?.cancel();
    var value = await stopRecording();
    voiceState = VoiceStateEnum.init;
    notifyListeners();
    return value;
  }

  Future<bool> _initRecorder() async {
    bool havePermission = await _checkPermission();
    if (!havePermission) {
      // 授权失败
      EasyLoading.showToast('请开启麦克风权限');
    }
    return havePermission;
  }

  /// 获取/判断权限
  Future<bool> _checkPermission() async {
    final status = await Permission.microphone.status;
    if (!status.isGranted) {
      // 无权限，则请求权限
      final agree = await PermissionDialogUtil.show(buildContext, [
        PermissionData(
            type: PermissionType.microphone,
            description: "用于口语发音练习与发音评测。开启后，您可通过录音完成跟读练习、对话练习。"),
      ]);
      if (agree != true) {
        return false;
      }
      PermissionStatus requestStatus = await Permission.microphone.request();
      return requestStatus == PermissionStatus.granted;
    } else {
      return true;
    }
  }

  void startRecord(
      {String? engineModelType, Function(String result)? cb}) async {
    try {
      if (_controller != null) {
        try {
          await _controller?.release();
        } catch (e) {}
      }
      _config = await AsrUtil.config();
      _config.audio_file_path =
          "${(await getTemporaryDirectory()).absolute.path}/temp.wav";
      if (engineModelType != null) {
        _config.engine_model_type = engineModelType;
      } else {
        _config.engine_model_type = "16k_zh";
      }
      _sentences = [];
      _realFinish.close;
      _realFinish = BehaviorSubject<bool>.seeded(false);
      LogUtil.d("engine_model_type:${_config.engine_model_type}");
      _controller = await _config.build();
      Stream<ASRData> asrStream = _controller!.recognize();
      await for (final val in asrStream) {
        switch (val.type) {
          case ASRDataType.SLICE:
          case ASRDataType.SEGMENT:
            var id = val.id!;
            var res = val.res!;
            if (id >= _sentences.length) {
              for (var i = _sentences.length; i <= id; i++) {
                _sentences.add("");
              }
            }
            _sentences[id] = res;
            result = _sentences.map((e) => e).join("");
            cb?.call(result);

            break;
          case ASRDataType.SUCCESS:
            result = val.result!;

            _sentences = [];
            cb?.call(result);
            break;
          case ASRDataType.NOTIFY:
            _realFinish.add(true);
        }
      }
    } on ASRError catch (e) {
      result = "错误码：${e.code} \n错误信息: ${e.message} \n详细信息: ${e.resp}";
      _realFinish.add(true);
      voiceState = VoiceStateEnum.init;
    } catch (e) {
      LogUtil.v(e.toString());
      _realFinish.add(true);
      voiceState = VoiceStateEnum.init;
    }
  }

  Future<bool> stopRecording() async {
    _timer?.cancel();
    try {
      await _controller?.stop();
    } catch (e) {}

    await for (final val in realFinish) {
      if (val == true) {
        return Future.value(true);
      }
    }
    return Future.value(true);
    // await _controller?.release();
  }
}
