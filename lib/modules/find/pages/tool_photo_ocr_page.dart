import 'dart:math';

import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/modules/find/pages/find_page.dart';
import 'package:flutter_app_kouyu/modules/find/provider/tool_photo_ocr_page_provider.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';

/// ocr 照片识别页面
class ToolPhotoOcrPage extends StatefulWidget {
  final ToolType toolType;
  final cbParam;
  const ToolPhotoOcrPage(
      {super.key, required this.toolType, this.cbParam = ''});

  static Route<void> route({required ToolType toolType, cbParam = ''}) {
    return MaterialPageRoute<void>(
        builder: (_) => ToolPhotoOcrPage(toolType: toolType, cbParam: cbParam));
  }

  @override
  _ToolPhotoOcrPageState createState() => _ToolPhotoOcrPageState();
}

class _ToolPhotoOcrPageState extends State<ToolPhotoOcrPage> {
  late final ImagePicker picker;

  @override
  void initState() {
    super.initState();
    picker = ImagePicker();
  }

  // @override
  // dispose() {
  //   Provider.of<ToolPhotoOcrPageProvider>(context, listen: false).dispose();
  //   super.dispose();
  // }

  @override
  Widget build(BuildContext context) {
    var barName = widget.toolType.getToolName();
    return ChangeNotifierProvider<ToolPhotoOcrPageProvider>(
      create: (_) => ToolPhotoOcrPageProvider(cbParam: widget.cbParam)
        ..askCameraPermission(context),
      child: Consumer<ToolPhotoOcrPageProvider>(
        builder: (context, provider, child) {
          return Stack(
            children: [
              Container(
                width: double.infinity,
                height: double.infinity,
                color: Colors.black,
              ),
              Scaffold(
                  backgroundColor: Colors.transparent,
                  appBar: AppBar(
                    leadingWidth: 300,
                    elevation: 0,
                    leading:
                        CustomAppbar.leftWhiteWidget(context, text: barName),
                  ),
                  floatingActionButtonLocation:
                      FloatingActionButtonLocation.centerDocked,
                  body: Container(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        FutureBuilder(
                            future: provider.initCamera(),
                            builder: (context, snapshot) {
                              if (snapshot.connectionState ==
                                  ConnectionState.done) {
                                if (snapshot.hasError) {
                                  return Text('初始化相机出错：${snapshot.error}');
                                }
                                return Container(
                                  width: double.infinity,
                                  height: 0.7.sh,
                                  color: Colors.black,
                                  child: provider.tempPictureFile.isEmpty
                                      ? CameraPreview(
                                          provider.cameraControllerTest!)
                                      : Image.asset(provider.tempPictureFile),
                                );
                              } else {
                                // return CircularProgressIndicator();
                                return Container(
                                    width: double.infinity,
                                    height: 0.7.sh,
                                    color: Colors.black);
                              }
                            }),
                        SizedBox(height: 20.w),
                        Text(barName + '(竖向拍摄才能正确识别)',
                            style: TextStyle(
                                color: Colors.white,
                                fontSize: 28.sp,
                                fontWeight: FontWeight.w400)),
                        SizedBox(height: 20.w),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Container(width: 130.w),
                            GestureDetector(
                                onTap: () => {
                                      if (provider.tempPictureFile.isEmpty)
                                        {
                                          provider.captureImage(),
                                        }
                                      else
                                        {
                                          provider.ocr(context),
                                        }
                                    },
                                child: provider.tempPictureFile.isEmpty
                                    ? Image.asset('images/take-photo.png',
                                        width: 100.w, height: 100.w)
                                    : Image.asset(
                                        'images/take_photo_finish.png',
                                        width: 100.w,
                                        height: 100.w)),
                            Container(
                              width: 130.w,
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  GestureDetector(
                                    onTap: () => {
                                      provider.tempPictureFile.isEmpty
                                          ? provider.pickImage()
                                          : provider.resetImage()
                                    },
                                    child: Image.asset(
                                        provider.tempPictureFile.isEmpty
                                            ? 'images/picture.png'
                                            : 'images/restart.png',
                                        width: 60.w,
                                        height: 60.w),
                                  ),
                                  Text(
                                      provider.tempPictureFile.isEmpty
                                          ? '从相册选择'
                                          : '返回',
                                      style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 24.sp,
                                          fontWeight: FontWeight.w400)),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  )),
            ],
          );
        },
      ),
    );
  }
}
