import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/common/utils/color_util.dart';
import 'package:flutter_app_kouyu/modules/find/pages/tool_translate_page.dart';
import 'package:flutter_app_kouyu/modules/find/provider/tool_voice_translate_page_provider.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_app_kouyu/widgets/play_button_widget.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

class ToolVoiceTranslatePage extends StatelessWidget {
  const ToolVoiceTranslatePage({super.key});

  static Route<void> route() {
    return MaterialPageRoute<void>(
        builder: (_) => const ToolVoiceTranslatePage());
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<ToolVoiceTranslatePageProvider>(
      create: (_) =>
          ToolVoiceTranslatePageProvider(buildContext: context)..loadData(),
      child: Consumer<ToolVoiceTranslatePageProvider>(
        builder: (context, provider, child) {
          return Stack(
            children: [
              Container(
                width: double.infinity,
                height: double.infinity,
                color: const Color(0xFFF3FBFD),
              ),
              Scaffold(
                backgroundColor: Colors.transparent,
                appBar: AppBar(
                  leadingWidth: 300,
                  backgroundColor: Colors.transparent,
                  elevation: 0,
                  leading: CustomAppbar.leftWidget(context, text: "同声传译"),
                ),
                floatingActionButtonLocation:
                    FloatingActionButtonLocation.centerDocked,
                body: Padding(
                    padding: EdgeInsets.all(0.w),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Padding(
                            padding: EdgeInsets.fromLTRB(24.w, 0, 24.w, 0),
                            child: _topSettingWidget(context, provider)),
                        SizedBox(height: 10.w),
                        // 一个高度为页面高度35%的组件, 当组件内容超出时, 可以滚动
                        Container(
                          // 宽度为100%
                          width: double.infinity,
                          padding: EdgeInsets.fromLTRB(24.w, 12.w, 24.w, 12.w),
                          height: 0.35.sh,
                          child: SingleChildScrollView(
                              child: Text(
                            provider.sttResult,
                            style: style_1_28_400,
                          )),
                        ),
                        // 一个高度为40%的组件, 当内容超出时, 可以滚动
                        Container(
                          // 宽度为100%
                          width: double.infinity,
                          padding: EdgeInsets.fromLTRB(24.w, 12.w, 24.w, 12.w),
                          // 蓝色背景
                          decoration: const BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Color(0xFF98ECEC),
                                Color(0xFF84E9FF),
                                Color(0xFF9BE1FF),
                              ],
                            ),
                          ),
                          height: 0.4.sh,
                          child: SingleChildScrollView(
                              child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text('翻译文本', style: style_1_24),
                                  _copyIconWidget(provider),
                                ],
                              ),
                              SizedBox(height: 10.w),
                              ...provider.translateContentTextArr.map((e) {
                                return Text(e, style: style_1_28_400);
                              }),
                            ],
                          )),
                        ),
                        _buttomButtonWidget(context, provider)
                      ],
                    )),
              )
            ],
          );
        },
      ),
    );
  }

  /// 顶部设置和状态组件, 包括中英文切换, 录音图标等等
  Widget _topSettingWidget(
      BuildContext context, ToolVoiceTranslatePageProvider model) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text('识别文本', style: style_1_24),
        Row(
          children: [
            model.recording
                ? Image.asset('images/voice-translate-dynamic.gif',
                    width: 120.w, height: 50.w, fit: BoxFit.cover)
                : Image.asset('images/voice-translate-static.png',
                    width: 60.w, height: 50.w, fit: BoxFit.cover),
            SizedBox(width: 20.w),
            Row(
              children: [
                Text(
                    model.translateMode == TranslateModeEnum.chineseToEnglish
                        ? "中文"
                        : "英文",
                    style: style_1_28_400),
                SizedBox(width: 20.w),
                // 一个点击就会切换中英文的图片
                GestureDetector(
                  onTap: () {
                    model.switchTranslateMode();
                  },
                  child: Image.network(
                    'https://cos.xinquai.com/user_upload/<EMAIL>',
                    width: 50.w,
                    height: 50.w,
                  ),
                ),
                SizedBox(width: 20.w),
                Text(
                    model.translateMode == TranslateModeEnum.chineseToEnglish
                        ? "英文"
                        : "中文",
                    style: style_1_28_400),
              ],
            )
          ],
        ),
      ],
    );
  }

  /// 复制小图标组件
  Widget _copyIconWidget(ToolVoiceTranslatePageProvider provider) {
    return GestureDetector(
        onTap: () {
          var content = provider.translateContent ?? "";
          if (content.isEmpty) {
            return;
          }
          Clipboard.setData(ClipboardData(text: content));
          EasyLoading.showToast("已复制");
        },
        child: Image(
          image: const AssetImage("images/chat_copy_blue.png"),
          width: 48.w,
          height: 48.w,
        ));
  }

  /// 语音播放组件
  Widget _voicePlayWidget(ToolVoiceTranslatePageProvider provider) {
    return PlayButtonWidget(
      style: PlayButtonStyle.blue,
      text: provider.translateContent,
    );
  }
}

/// 底部按钮组件, 固定在页面底部
/// 一个开始录音, 一个结束录音
Widget _buttomButtonWidget(
    BuildContext context, ToolVoiceTranslatePageProvider provider) {
  return Expanded(
    child: SizedBox(
      // 块度为100%
      width: double.infinity,
      height: 0.2.sh,
      // 高度为
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        // 两个按钮, 一个开始录音, 一个结束录音
        children: [
          GestureDetector(
              onTap: () {
                provider.clickLeftBtn(context);
              },
              // 带图标的按钮
              child: Container(
                // border radius 为12 1px solid #D8D8D8
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(24.w),
                    border: Border.all(color: ColorUtil.black1)),
                width: 320.w,
                height: 102.w,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.network(
                        provider.recording
                            ? 'https://cos.xinquai.com/user_upload/<EMAIL>'
                            : 'https://cos.xinquai.com/user_upload/<EMAIL>',
                        height: 50.w,
                        width: 50.w),
                    SizedBox(width: 20.w),
                    Text(provider.recording ? '停止' : '开始', style: style_1_24)
                  ],
                ),
              )),
          GestureDetector(
            onTap: () {
              provider.clickFinishBtn(context);
            },
            child: Container(
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24.w),
                  border: Border.all(color: ColorUtil.black1)),
              width: 320.w,
              height: 102.w,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.network(
                    'https://cos.xinquai.com/user_upload/<EMAIL>',
                    height: 50.w,
                    width: 50.w,
                  ),
                  SizedBox(width: 20.w),
                  Text('结束', style: style_1_24)
                ],
              ),
            ),
          ),
        ],
      ),
    ),
  );
}
