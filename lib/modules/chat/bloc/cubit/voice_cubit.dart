import 'dart:async';

import 'package:asr_plugin/asr_plugin.dart';
import 'package:audio_session/audio_session.dart';
import 'package:common_utils/common_utils.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/asr/asr_util.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/ch_to_en_audio_model/ch_to_en_audio_model.dart';
import 'package:flutter_app_kouyu/common/utils/common_util.dart';
import 'package:flutter_app_kouyu/common/utils/log.dart';
import 'package:flutter_app_kouyu/common/utils/login_util.dart';
import 'package:flutter_app_kouyu/common/utils/permission_dialog_util.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/chat_cubit.dart';
import 'package:flutter_app_kouyu/repository/free_talk_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:rxdart/rxdart.dart';

part 'voice_state.dart';

abstract class VoiceEvent {
  recordFinish(String? question, String filePath);
}

class VoiceCubit extends Cubit<VoiceState> {
  final FreeTalkRepository freeTalkRepository = FreeTalkRepository();
  final TextEditingController _textEditingController = TextEditingController();
  final TextEditingController inputTextEditingController =
      TextEditingController();

  final ScrollController scrollController = ScrollController();
  bool showTextInout;

  Timer? _timer;
  final maxSeconds = 150;

  String _result = "";
  List<String> _sentences = [];
  late ASRControllerConfig _config;
  ASRController? _controller;
  BehaviorSubject<bool> _realFinish = BehaviorSubject<bool>.seeded(false);
  ValueStream<bool> get realFinish => _realFinish.stream;
  // 0: 自由聊天 1: 教学模式
  int model;
  BuildContext buildContext;
  VoiceCubit(this.buildContext, {this.showTextInout = false, this.model = 0})
      : super(const VoiceState());

  @override
  Future<void> close() async {
    await stopRecording();
    return super.close();
  }

  updateInputText(String text) {
    emit(state.copywith(text: text));
  }

  clearInputText() {
    inputTextEditingController.text = "";
    emit(state.copywith(text: null));
  }

  TextEditingController get textEditingController {
    _textEditingController.text = state.getTitle(model: model);
    return _textEditingController;
  }

  TextEditingController get textEditingController2 {
    _textEditingController.text = state.getTitle2(model: model);
    return _textEditingController;
  }

  void chineseHelp(VoiceEvent chatCubit) async {
    bool access = await _initRecorder();
    if (!access) {
      return;
    }
    emit(state.copywith(voiceType: VoiceTypeEnum.ch, seconds: 0, text: ""));
    startProgress(chatCubit);
    startRecord(engineModelType: "16k_zh");
  }

  //isZh 是否包含中文
  void speakEn({bool? isZh, required VoiceEvent chatCubit}) async {
    //停止播放声音
    await CommonUtils.stopPlay();

    await Future.delayed(const Duration(milliseconds: 200));

    bool access = await _initRecorder();
    if (!access) {
      return;
    }
    emit(state.copywith(voiceType: VoiceTypeEnum.en, seconds: 0, text: ""));
    startProgress(chatCubit);
    startRecord(engineModelType: isZh == true ? "16k_zh" : "16k_en");
  }

  void followRead(String? fllowText, VoiceEvent chatCubit) async {
    //停止播放声音
    await CommonUtils.stopPlay();
    bool access = await _initRecorder();
    if (!access) {
      return;
    }
    emit(state.copywith(
        voiceType: VoiceTypeEnum.follow,
        seconds: 0,
        text: "",
        followTest: fllowText));
    startProgress(chatCubit);
    startRecord(engineModelType: "16k_en");
  }

  Future<bool> stop() async {
    _timer?.cancel();
    emit(state.copywith(voiceType: VoiceTypeEnum.init, seconds: 0));
    var value = await stopRecording();
    return value;
  }

  Future<bool> _initRecorder() async {
    bool havePermission = await _checkPermission();
    if (!havePermission) {
      // 授权失败
      EasyLoading.showToast('请开启麦克风权限');
    }
    return havePermission;
  }

  /// 获取/判断权限
  Future<bool> _checkPermission() async {
    final status = await Permission.microphone.status;
    if (!status.isGranted) {
      // 无权限，则请求权限
      final agree = await PermissionDialogUtil.show(buildContext, [
        PermissionData(
            type: PermissionType.microphone,
            description: "用于口语发音练习与发音评测。开启后，您可通过录音完成跟读练习、对话练习。"),
      ]);
      if (agree != true) {
        return false;
      }
      PermissionStatus requestStatus = await Permission.microphone.request();
      return requestStatus == PermissionStatus.granted;
    } else {
      return true;
    }
  }

  void startRecord({String? engineModelType}) async {
    try {
      if (_controller != null) {
        try {
          await _controller?.release();
        } catch (e) {}
      }
      _config = await AsrUtil.config();
      _config.audio_file_path =
          "${(await getTemporaryDirectory()).absolute.path}/temp.wav";
      if (engineModelType != null) {
        _config.engine_model_type = engineModelType;
      } else {
        _config.engine_model_type = "16k_zh";
      }
      _sentences = [];
      _realFinish.close;
      _realFinish = BehaviorSubject<bool>.seeded(false);
      LogUtil.d("engine_model_type:${_config.engine_model_type}");

      final audioSession = await AudioSession.instance;
      await audioSession.configure(const AudioSessionConfiguration.speech());
      await audioSession.setActive(true);

      _controller = await _config.build();
      Stream<ASRData> asrStream = _controller!.recognize();
      await for (final val in asrStream) {
        switch (val.type) {
          case ASRDataType.SLICE:
          case ASRDataType.SEGMENT:
            var id = val.id!;
            var res = val.res!;
            if (id >= _sentences.length) {
              for (var i = _sentences.length; i <= id; i++) {
                _sentences.add("");
              }
            }
            _sentences[id] = res;
            _result = _sentences.map((e) => e).join("");
            Log.v("RESULT:${_result}");

            emit(state.copywith(text: _result));

            break;
          case ASRDataType.SUCCESS:
            _result = val.result!;
            Log.v("SUCCESS:${val.result}");

            _sentences = [];
            emit(state.copywith(text: _result));
            break;
          case ASRDataType.NOTIFY:
            Log.v("NOTIFY:${val.info}");
        }
      }
    } on ASRError catch (e) {
      Log.v("${e.code},${e.message}");

      _result = "错误码：${e.code} \n错误信息: ${e.message} \n详细信息: ${e.resp}";
      emit(state.copywith(voiceType: VoiceTypeEnum.init, seconds: 0));
    } catch (e) {
      LogUtil.v(e.toString());
      emit(state.copywith(voiceType: VoiceTypeEnum.init, seconds: 0));
    } finally {
      _realFinish.add(true);
    }
  }

  Future<bool> stopRecording() async {
    _timer?.cancel();
    try {
      await _controller?.stop();

      await _controller?.release();
    } catch (e) {}
    // await for (final val in realFinish) {
    //   if (val == true) {
    //     Log.v("录音结束");
    //     return Future.value(true);
    //   }
    // }
    // await Future.delayed(const Duration(seconds: 1));

    return Future.value(true);
  }

  void action(VoiceEvent chatCubit) async {
    if (state.voiceType == VoiceTypeEnum.en ||
        state.voiceType == VoiceTypeEnum.follow) {
      //发送
      stop().then((value) {
        if (value) {
          chatCubit.recordFinish(state.text, _config.audio_file_path);
        }
      });

      //停止录音和计时
    } else if (state.voiceType == VoiceTypeEnum.ch) {
      //翻译
      stopRecording();
      String text = state.text;
      emit(state.copywith(
          voiceType: VoiceTypeEnum.cnTanslating, seconds: 0, text: ""));
      freeTalkRepository.chToEn(text).then((value) {
        emit(state.copywith(
            voiceType: VoiceTypeEnum.cnTanslated,
            seconds: 0,
            text: value.data!.reply!,
            chToEnAudioModel: value));
      }, onError: (_) {
        emit(state.copywith(
          voiceType: VoiceTypeEnum.cnTanslateFaild,
          seconds: 0,
          text: "",
        ));
      });
    }
  }

  void startProgress(VoiceEvent chatCubit) {
    _timer = Timer.periodic(const Duration(seconds: 1), (_) {
      int seconds = state.seconds + 1;
      if (seconds > maxSeconds) {
        //发送
        stop().then((value) {
          if (value) {
            chatCubit.recordFinish(state.text, _config.audio_file_path);
          }
        });
      }
      emit(state.copywith(seconds: state.seconds + 1));
    });
  }

  double getProgress() {
    return state.seconds / maxSeconds;
  }

  void showTextInput(BuildContext context) {
    emit(state.copywith(voiceType: VoiceTypeEnum.textInput));
  }
}
