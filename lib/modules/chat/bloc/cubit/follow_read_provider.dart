// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:ui';

import 'package:flutter/material.dart';

import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/audio_upload_model/audio_upload_model.dart';
import 'package:flutter_app_kouyu/common/http/models/scene_topics_vocabulary_model/list.dart';
import 'package:flutter_app_kouyu/common/http/models/scene_topics_vocabulary_model/scene_topics_vocabulary_model.dart';
import 'package:flutter_app_kouyu/common/http/models/sence_topic_sentence/list.dart';
import 'package:flutter_app_kouyu/common/http/models/sence_topic_sentence/sence_topic_sentence.dart';
import 'package:flutter_app_kouyu/common/http/models/soe_model/soe_model.dart';
import 'package:flutter_app_kouyu/common/http/models/vocabulary_soe_model/vocabulary_soe_model.dart';
import 'package:flutter_app_kouyu/common/provider/voice_model.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class FollowReadProvider extends VoiceModel {
  SceneTopicsVocabularyModel? sceneTopicsVocabularyModel;
  SenceTopicSentence? senceTopicSentenceModel;
  String text;
  num conversationId;
  SoeModel? soeModel;
  FollowReadProvider(
      {required this.text,
      required this.conversationId,
      required super.buildContext});

  @override
  void dispose() {
    stopRecording();
    super.dispose();
  }

  Future<bool> sent() async {
    await super.stop();
    notifyListeners();
    EasyLoading.show(status: "测评中...");
    try {
      //上传文件
      AudioUploadModel audioUploadModel =
          await Api.uploadFile(filePath, topicCode: "", scene: "");
      //soe
      soeModel = await Api.soe({
        "text": text,
        "audio_url": audioUploadModel.data?.fileUrl ?? "",
        "client_sentence_id": 0,
        "conversation_id": conversationId,
        "scene": "",
      });
    } finally {
      EasyLoading.dismiss();
    }

    notifyListeners();

    return true;
  }

  followFinish() {}
}
