import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/models/scene_topics_vocabulary_model/list.dart';
import 'package:flutter_app_kouyu/common/http/models/sence_topic_sentence/list.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/vocabulary_partice_provider.dart';
import 'package:flutter_app_kouyu/modules/chat/view/vocabulary_practice_card_widget.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_app_kouyu/widgets/play_button_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

class VocabularyPracticePage extends StatelessWidget {
  const VocabularyPracticePage({super.key});

  static Route<void> route(RouteSettings? routeSettings) {
    return MaterialPageRoute<void>(
        builder: (_) => const VocabularyPracticePage(),
        settings: routeSettings);
  }

  @override
  Widget build(BuildContext context) {
    final args = ModalRoute.of(context)!.settings.arguments as Map;
    String topicCode = args["topic_code"]!;
    String topicName = args["topic_name"] ?? "_";

    return ChangeNotifierProvider<VocabularyPracticeProvider>(
      create: (_) => VocabularyPracticeProvider(
          topicCode: topicCode, buildContext: context)
        ..refreshData(),
      child: Consumer<VocabularyPracticeProvider>(
        builder: (context, model, child) {
          return Scaffold(
            backgroundColor: bgColor,
            appBar: AppBar(
              leadingWidth: 100,
              elevation: 0,
              leading: CustomAppbar.leftWidget(context, text: ""),
              title: Text(
                topicName,
                style: tsBar,
              ),
              centerTitle: true,
            ),
            body: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.max,
              children: [
                Padding(
                  padding: EdgeInsets.only(top: 24.w, left: 40.w, right: 40.w),
                  child: Row(
                    children: [
                      GestureDetector(
                        onTap: () {
                          model.type = 1;
                        },
                        child: Stack(
                          alignment: Alignment.bottomCenter,
                          children: [
                            Text(
                              "单词练习",
                              style: model.type == 1 ? style_1_36 : style_2_36,
                            ),
                            if (model.type == 1)
                              Image.asset(
                                "images/text_bg.png",
                                width: 63.w,
                                height: 16.w,
                              )
                          ],
                        ),
                      ),
                      SizedBox(
                        width: 40.w,
                      ),
                      GestureDetector(
                        onTap: () {
                          model.type = 2;
                        },
                        child: Stack(
                          alignment: Alignment.bottomCenter,
                          children: [
                            Text(
                              "句子练习",
                              style: model.type == 2 ? style_1_36 : style_2_36,
                            ),
                            if (model.type == 2)
                              Image.asset(
                                "images/text_bg.png",
                                width: 63.w,
                                height: 16.w,
                              )
                          ],
                        ),
                      ),
                      const Spacer(),
                      GestureDetector(
                        onTap: () {
                          if (model.showType == 1) {
                            model.showType = 2;
                          } else {
                            model.showType = 1;
                          }
                        },
                        child: Container(
                          width: 179.w,
                          height: 60.w,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20.w),
                              color: Colors.white),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Image.asset(
                                "images/card_change.png",
                                width: 36.w,
                                height: 36.w,
                              ),
                              SizedBox(
                                width: 8.w,
                              ),
                              Text(
                                model.showType == 1 ? "切换卡片" : "切换列表",
                                style: style_1_24,
                              )
                            ],
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                SizedBox(
                  height: 24.w,
                ),
                Visibility(
                    visible: model.type == 1 && model.showType == 1,
                    child: Expanded(child: _vocabularyWidget(context))),
                Visibility(
                    visible: model.type == 2 && model.showType == 1,
                    child: Expanded(child: _sentsenceWidget(context))),
                Visibility(
                  visible: model.showType == 2,
                  child: const Expanded(child: VocabularyPracticeCardWidget()),
                )
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _vocabularyWidget(BuildContext context) {
    return CustomScrollView(
      slivers: [..._vocabularyList(context)],
    );
  }

  Widget _sentsenceWidget(BuildContext context) {
    return CustomScrollView(
      slivers: [..._sentsenceList(context)],
    );
  }

  List<Widget> _vocabularyList(BuildContext context) {
    VocabularyPracticeProvider model =
        context.read<VocabularyPracticeProvider>();
    return [
      SliverToBoxAdapter(
        child: Padding(
          padding: EdgeInsets.only(left: 40.w, right: 40.w, bottom: 24.w),
          child: Text(
            "已学习${model.sceneTopicsVocabularyModel?.data?.hasLearnedCount ?? "_"}个/共${model.sceneTopicsVocabularyModel?.data?.listSize ?? "_"}",
            style: style_2_24,
          ),
        ),
      ),
      SliverToBoxAdapter(
        child: Container(
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(24.w)),
          clipBehavior: Clip.hardEdge,
          margin:
              EdgeInsets.only(top: 24.w, right: 40.w, bottom: 24.w, left: 40.w),
          child: ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemBuilder: (context, index) {
                Vocabulary vocabulary =
                    model.sceneTopicsVocabularyModel!.data!.list![index];
                return GestureDetector(
                  onTap: () {
                    model.jumpToVocabularyCard(index);
                  },
                  child: _wordItemWidget(context, vocabulary),
                );
              },
              separatorBuilder: (context, index) {
                return Container(
                  color: separated,
                  height: 1.w,
                  width: double.infinity,
                );
              },
              itemCount:
                  model.sceneTopicsVocabularyModel?.data?.list?.length ?? 0),
        ),
      )
    ];
  }

  List<Widget> _sentsenceList(BuildContext context) {
    VocabularyPracticeProvider model =
        context.read<VocabularyPracticeProvider>();
    return [
      SliverToBoxAdapter(
        child: Padding(
          padding: EdgeInsets.only(left: 40.w, right: 40.w, bottom: 24.w),
          child: Text(
            "已学习${model.senceTopicSentenceModel?.data?.hasLearnedCount ?? "_"}个/共${model.senceTopicSentenceModel?.data?.listSize ?? "_"}",
            style: style_2_24,
          ),
        ),
      ),
      SliverToBoxAdapter(
        child: Container(
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(24.w)),
          clipBehavior: Clip.hardEdge,
          margin:
              EdgeInsets.only(top: 24.w, right: 40.w, bottom: 24.w, left: 40.w),
          child: ListView.separated(
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemBuilder: (context, index) {
                Sentence sentence =
                    model.senceTopicSentenceModel!.data!.list![index];
                return GestureDetector(
                  onTap: () {
                    model.jumpToSentenceCard(index);
                  },
                  child: _sentenceItemWidget(sentence),
                );
              },
              separatorBuilder: (context, index) {
                return Container(
                  color: separated,
                  height: 1.w,
                  width: double.infinity,
                );
              },
              itemCount:
                  model.senceTopicSentenceModel?.data?.list?.length ?? 0),
        ),
      )
    ];
  }

  Container _wordItemWidget(BuildContext context, Vocabulary vocabulary) {
    VocabularyPracticeProvider model =
        context.read<VocabularyPracticeProvider>();

    return Container(
      color: Colors.white,
      padding:
          EdgeInsets.only(left: 32.w, right: 32.w, top: 24.w, bottom: 24.w),
      child: Column(
        children: [
          Row(
            children: [
              PlayButtonWidget(
                style: PlayButtonStyle.blue,
                audioUrl: vocabulary.defaultPronunciationUrl,
              ),
              SizedBox(
                width: 16.w,
              ),
              Text(
                vocabulary.vocabulary ?? "_",
                style: style_1_28,
              ),
              const Spacer(),
              Text(
                vocabulary.learned == true ? "已学会" : "未学会",
                style: style_green_24,
              )
            ],
          ),
          Row(
            children: [
              SizedBox(
                width: 64.w,
              ),
              Expanded(
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        vocabulary.definition ?? "_",
                        style: style_2_24,
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        if (vocabulary.collected == true) {
                          model.cancelCollectVocabulary(vocabulary);
                        } else {
                          model.collectVocabulary(vocabulary);
                        }
                      },
                      child: Image.asset(
                        vocabulary.collected == true
                            ? "images/start_yellow.png"
                            : "images/start_blue.png",
                        width: 34.w,
                        height: 32.w,
                      ),
                    )
                  ],
                ),
              )
            ],
          )
        ],
      ),
    );
  }

  Container _sentenceItemWidget(Sentence sentence) {
    return Container(
      color: Colors.white,
      padding:
          EdgeInsets.only(left: 32.w, right: 32.w, top: 24.w, bottom: 24.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            sentence.roleName ?? "",
            style: style_2_24,
          ),
          SizedBox(
            height: 16.w,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.baseline,
            textBaseline: TextBaseline.alphabetic,
            children: [
              PlayButtonWidget(
                style: PlayButtonStyle.blue,
                audioUrl: sentence.defaultPronunciationUrl,
              ),
              SizedBox(
                width: 16.w,
              ),
              Expanded(
                child: Text(
                  sentence.sentence ?? "_",
                  style: style_1_28,
                ),
              ),
              Text(
                sentence.learned == true ? "已学会" : "未学会",
                style: style_green_24,
              )
            ],
          ),
          Row(
            children: [
              SizedBox(
                width: 64.w,
              ),
              Expanded(
                child: Expanded(
                  child: Text(
                    sentence.chineseMeaning ?? "_",
                    style: style_2_24,
                  ),
                ),
              )
            ],
          )
        ],
      ),
    );
  }
}
