import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_app_kouyu/common/http/loading_status.dart';
import 'package:flutter_app_kouyu/common/http/models/analyze_model/analyze_model.dart';
import 'package:flutter_app_kouyu/common/http/models/soe_model/soe_model.dart';
import 'package:flutter_app_kouyu/common/utils/common_util.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/sence_analysis_model.dart';
import 'package:flutter_app_kouyu/modules/chat/view/follow_read_widget.dart';
import 'package:flutter_app_kouyu/modules/chat/view/free_talk_page.dart';
import 'package:flutter_app_kouyu/repository/free_talk_repository.dart';
import 'package:flutter_app_kouyu/widgets/play_button_widget.dart';
import 'package:flutter_app_kouyu/widgets/selected_text.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:gif_view/gif_view.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/common/utils/color_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

class SoeWidgetNew extends StatelessWidget {
  final SoeModel? soeModel;
  final AnalyzeModel? analyzeModel;
  final TalkModel talkModel;
  final TalkArgument argument;

  const SoeWidgetNew(
      {super.key,
      required this.soeModel,
      this.analyzeModel,
      required this.talkModel,
      required this.argument});
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<SenceAnalysisModel>(create: (_) {
      return SenceAnalysisModel(
          soeModel: soeModel,
          analyzeModel: analyzeModel,
          talkModel: talkModel,
          argument: argument);
    }, child: Consumer<SenceAnalysisModel>(builder: (context, model, child) {
      return Container(
        padding:
            EdgeInsets.only(left: 40.w, right: 40.w, bottom: 60.w, top: 20.w),
        decoration: BoxDecoration(
            color: bgColor,
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(40.w),
                topRight: Radius.circular(40.w))),
        height: 1000,
        clipBehavior: Clip.hardEdge,
        width: double.infinity,
        child: ListView(
          children: [
            SizedBox(
              height: 32.w,
            ),
            Text(
              "详细分析",
              style: style_1_36,
            ),
            Container(
                padding: EdgeInsets.fromLTRB(0.w, 24.w, 0.w, 24.w),
                child: _reviewWidget(context)),
          ],
        ),
      );
    }));
  }

  Container _voiceWidget(String type, String url) {
    return Container(
      height: 48.w,
      decoration: BoxDecoration(
          border: Border.all(color: ColorUtil.separated),
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.w)),
      child: GestureDetector(
        onTap: () {
          CommonUtils.playVideo(url);
        },
        child: Row(
          children: [
            SizedBox(
              width: 24.w,
            ),
            Text(
              type,
              style: style_1_24,
            ),
            PlayButtonWidget2(
              audioUrl: url,
            ),
          ],
        ),
      ),
    );
  }

  Container _lineWidget() {
    return Container(
      height: 1.w,
      width: double.infinity,
      color: ColorUtil.separated,
    );
  }

  Widget _reviewWidget(BuildContext context) {
    SenceAnalysisModel model = context.read<SenceAnalysisModel>();

    return Column(
      children: [
        _originTextWidget(context),
        SizedBox(
          height: 20.w,
        ),
        Row(
          children: [
            _voiceWidget("美音", soeModel?.data?.usAudioUrl ?? ""),
            SizedBox(
              width: 16.w,
            ),
            _voiceWidget("英音", soeModel?.data?.ukAudioUrl ?? ""),
            const Spacer(),
            GestureDetector(
              onTap: () {
                _gotoFollowRead(context, soeModel?.data?.text ?? "");
              },
              child: Container(
                height: 56.w,
                width: 143.w,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16.w),
                    color: const Color(0xFF2693FF)),
                child: Center(
                  child: Text(
                    "再读一遍",
                    style: style_1_24.copyWith(color: Colors.white),
                  ),
                ),
              ),
            ),
          ],
        ),
        SizedBox(
          height: 20.w,
        ),
        _voiceSoeWidget(),
        Padding(
          padding: EdgeInsets.only(left: 24.w, right: 24.w),
          child: _lineWidget(),
        ),
        if (model.analyzeModel?.data?.correct == false)
          _analysisWidget(context, model),
        _suggessWidget(context, model)
      ],
    );
  }

  void _gotoFollowRead(BuildContext context, String text) {
    showModalBottomSheet(
        isScrollControlled: true,
        useSafeArea: true,
        constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height -
                MediaQuery.of(context).padding.top -
                160),
        backgroundColor: Colors.transparent,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20), topRight: Radius.circular(20))),
        context: context,
        builder: (context) {
          return FollowReadWidget(
            text: text,
            conversationId: talkModel.conversationId ?? 0,
          );
        });
  }

  Widget _voiceSoeWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.fromLTRB(32.w, 24.w, 32.w, 24.w),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(24.w),
                  topRight: Radius.circular(24.w)),
              color: Colors.white),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "发音",
                style: style_1_28,
              ),
              SizedBox(
                width: 147.w,
                height: 95.w,
                child: Stack(
                  children: [
                    Align(
                      alignment: Alignment.bottomCenter,
                      child: Image.asset(
                        "images/score_bg.png",
                        width: 147.w,
                        height: 40.w,
                      ),
                    ),
                    Align(
                        alignment: Alignment.topLeft,
                        child: Text(
                          soeModel?.data?.suggestedScore?.toString() ?? "_",
                          style: style_green_64,
                        )),
                  ],
                ),
              ),
              Text(
                "表达流利顺畅，和你交流是件愉悦的事",
                style: style_2_28,
              ),
              SizedBox(
                height: 24.w,
              ),
              Row(
                children: [
                  Column(
                    children: [
                      _progressWidget(
                        '准确度',
                        soeModel?.data?.pronAccuracy,
                      ),
                      SizedBox(
                        height: 32.w,
                      ),
                      _progressWidget(
                        '流畅度',
                        soeModel?.data?.pronFluency,
                      ),
                      SizedBox(
                        height: 32.w,
                      ),
                      _progressWidget('完整度', soeModel?.data?.pronCompletion),
                    ],
                  ),
                  Column(
                    children: [
                      Container(
                        width: 346.w,
                        height: 193.w,
                        decoration: const BoxDecoration(
                            image: DecorationImage(
                                image: AssetImage("images/voice_speech_bg.png"),
                                fit: BoxFit.fill)),
                        child: Stack(
                          alignment: Alignment.topCenter,
                          children: [
                            Positioned.fill(
                              child: Column(
                                children: [
                                  SizedBox(
                                    height: 40.w,
                                  ),
                                  Transform.rotate(
                                    angle: ((soeModel?.data?.speed ?? 0) - 90) /
                                        180 *
                                        pi,
                                    alignment: Alignment.bottomCenter,
                                    child: Image.asset(
                                      "images/voice_speech_arrow.png",
                                      width: 12.w,
                                      height: 116.w,
                                      fit: BoxFit.fill,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Positioned(
                                top: 60.w,
                                child: Center(
                                  child: Text(
                                    "${soeModel?.data?.speed ?? "_"}",
                                    style: style_1_48,
                                  ),
                                ))
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 10.w,
                      ),
                      RichText(
                          textAlign: TextAlign.justify,
                          text: TextSpan(children: [
                            TextSpan(
                              text: soeModel?.data?.speedComment ?? "_",
                              style: style_bue_24,
                            ),
                            // WidgetSpan(
                            //     child: SizedBox(
                            //   width: 10.w,
                            // )),
                            // WidgetSpan(
                            //     alignment: PlaceholderAlignment.middle,
                            //     child: Image.asset(
                            //       "images/blue_help.png",
                            //       width: 20.w,
                            //       height: 20.w,
                            //       fit: BoxFit.fill,
                            //     )),
                          ])),
                    ],
                  )
                ],
              )
            ],
          ),
        ),
      ],
    );
  }

  Widget _analysisWidget(BuildContext context, SenceAnalysisModel model) {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Container(
          padding: EdgeInsets.fromLTRB(32.w, 24.w, 32.w, 24.w),
          color: Colors.white,
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Text(
              "语法",
              style: style_1_28,
            ),
            Container(
                padding: EdgeInsets.fromLTRB(32.w, 24.w, 32.w, 24.w),
                margin: EdgeInsets.fromLTRB(0.w, 24.w, 0.w, 24.w),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(24.w), color: btnColor),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "建议使用",
                      style: style_2_24,
                    ),
                    SizedBox(
                      height: 16.w,
                    ),
                    SelectableTextUtil.editableText(
                      context,
                      text: analyzeModel?.data?.correction ?? "_",
                      style: style_1_32_400,
                    ),
                    ..._translateWidgets(
                        context, analyzeModel?.data?.correction),
                    SizedBox(
                      height: 24.w,
                    ),
                    Row(
                      children: [
                        PlayButtonWidget(
                          style: PlayButtonStyle.white,
                          text: analyzeModel?.data?.correction,
                        ),
                        SizedBox(
                          width: 20.w,
                        ),
                        GestureDetector(
                            onTap: () {
                              model.translate(analyzeModel?.data?.correction);
                            },
                            child: Image(
                              image: const AssetImage(
                                  "images/chat_translate_white.png"),
                              width: 48.w,
                              height: 48.w,
                            )),
                        SizedBox(
                          width: 20.w,
                        ),
                        GestureDetector(
                            onTap: () {
                              Clipboard.setData(ClipboardData(
                                  text: analyzeModel?.data?.correction ?? ""));
                              EasyLoading.showToast("已复制");
                            },
                            child: Image(
                              image: const AssetImage(
                                  "images/chat_copy_white.png"),
                              width: 48.w,
                              height: 48.w,
                            )),
                        SizedBox(
                          width: 20.w,
                        ),
                        GestureDetector(
                            onTap: () {
                              model.collection(analyzeModel?.data?.correction);
                            },
                            child: Image(
                              image: AssetImage(model.collectedList.contains(
                                          analyzeModel?.data?.correction) ==
                                      true
                                  ? "images/chat_collected.png"
                                  : "images/chat_collect_white.png"),
                              width: 48.w,
                              height: 48.w,
                            )),
                        SizedBox(
                          width: 20.w,
                        ),
                        const Spacer(),
                        GestureDetector(
                            onTap: () {
                              model.gotoFollowRead(context,
                                  analyzeModel?.data?.correction ?? "");
                            },
                            child: Image.asset(
                              "images/fllow_btn2.png",
                              height: 48.w,
                            ))
                      ],
                    ),
                  ],
                )),
            Text(
              analyzeModel?.data?.explanation ?? "_",
              style: style_2_28,
            ),
          ])),
      Padding(
        padding: EdgeInsets.only(left: 24.w, right: 24.w),
        child: _lineWidget(),
      ),
    ]);
  }

  Widget _suggessWidget(BuildContext context, SenceAnalysisModel model) {
    if (model.englishTapModel?.data == null ||
        model.englishTapModel!.data!.isEmpty) {
      return Container();
    }
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Container(
          padding: EdgeInsets.fromLTRB(32.w, 24.w, 32.w, 24.w),
          color: Colors.white,
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Text(
              "润色",
              style: style_1_28,
            ),
            SizedBox(
              height: 16.w,
            ),
            Wrap(
              spacing: 26.w,
              children: [
                if (model.englishTapModel?.data != null)
                  ...model.englishTapModel!.data!.map((e) {
                    return e.type == model.polishedEnglishLabel?.type
                        ? Container(
                            padding:
                                EdgeInsets.fromLTRB(20.w, 12.w, 20.w, 12.w),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(16.w),
                                gradient: ColorUtil.blueToGreen),
                            child: Text(
                              e.name ?? "_",
                              style: style_1_24,
                            ),
                          )
                        : GestureDetector(
                            onTap: () {
                              model.setPolishedEnglishLabel(e);
                            },
                            child: Container(
                              padding:
                                  EdgeInsets.fromLTRB(20.w, 12.w, 20.w, 12.w),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10.w),
                                  color: const Color(0xFFF0F8FA)),
                              child: Text(
                                e.name ?? "_",
                                style: style_1_24,
                              ),
                            ),
                          );
                  }),
              ],
            ),
            if (model.polishedEnglishLabel?.loadingStatus ==
                LoadingStatus.inProgress)
              Container(
                  padding: EdgeInsets.fromLTRB(32.w, 24.w, 32.w, 24.w),
                  margin: EdgeInsets.fromLTRB(0.w, 24.w, 0.w, 24.w),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(24.w),
                      color: btnColor),
                  child: Row(
                    children: [
                      GifView.asset(
                        "images/samll_loading.gif",
                        fit: BoxFit.fitWidth,
                        width: 32.w,
                        height: 32.w,
                        repeat: ImageRepeat.repeat,
                      ),
                    ],
                  )),
            if (model.polishedEnglishLabel?.loadingStatus !=
                LoadingStatus.inProgress)
              ..._polishedEnglishWidgets(model, context),
          ]))
    ]);
  }

  List<Widget> _polishedEnglishWidgets(
      SenceAnalysisModel model, BuildContext context) {
    return [
      Container(
          padding: EdgeInsets.fromLTRB(32.w, 24.w, 32.w, 24.w),
          margin: EdgeInsets.fromLTRB(0.w, 24.w, 0.w, 24.w),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(24.w), color: btnColor),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "建议使用",
                style: style_2_24,
              ),
              SizedBox(
                height: 16.w,
              ),
              SelectableTextUtil.editableText(
                context,
                text: model.polishedEnglishLabel?.sentencPolishingModel?.data
                        ?.polishedSentence ??
                    "_",
                style: style_1_32_400,
              ),
              ..._translateWidgets(
                  context,
                  model.polishedEnglishLabel?.sentencPolishingModel?.data
                      ?.polishedSentence),
              SizedBox(
                height: 24.w,
              ),
              Row(
                children: [
                  PlayButtonWidget(
                    style: PlayButtonStyle.white,
                    text: model.polishedEnglishLabel?.sentencPolishingModel
                        ?.data?.polishedSentence,
                  ),
                  SizedBox(
                    width: 20.w,
                  ),
                  GestureDetector(
                      onTap: () {
                        model.translate(model.polishedEnglishLabel
                            ?.sentencPolishingModel?.data?.polishedSentence);
                      },
                      child: Image(
                        image:
                            const AssetImage("images/chat_translate_white.png"),
                        width: 48.w,
                        height: 48.w,
                      )),
                  SizedBox(
                    width: 20.w,
                  ),
                  GestureDetector(
                      onTap: () {
                        Clipboard.setData(ClipboardData(
                            text: model
                                    .polishedEnglishLabel
                                    ?.sentencPolishingModel
                                    ?.data
                                    ?.polishedSentence ??
                                ""));
                        EasyLoading.showToast("已复制");
                      },
                      child: Image(
                        image: const AssetImage("images/chat_copy_white.png"),
                        width: 48.w,
                        height: 48.w,
                      )),
                  SizedBox(
                    width: 20.w,
                  ),
                  GestureDetector(
                      onTap: () {
                        model.collection(model.polishedEnglishLabel
                            ?.sentencPolishingModel?.data?.polishedSentence);
                      },
                      child: Image(
                        image: AssetImage(model.collectedList.contains(model
                                    .polishedEnglishLabel
                                    ?.sentencPolishingModel
                                    ?.data
                                    ?.polishedSentence) ==
                                true
                            ? "images/chat_collected.png"
                            : "images/chat_collect_white.png"),
                        width: 48.w,
                        height: 48.w,
                      )),
                  SizedBox(
                    width: 20.w,
                  ),
                  const Spacer(),
                  GestureDetector(
                      onTap: () {
                        model.gotoFollowRead(
                            context,
                            model.polishedEnglishLabel?.sentencPolishingModel
                                    ?.data?.polishedSentence ??
                                "");
                      },
                      child: Image.asset(
                        "images/fllow_btn2.png",
                        height: 48.w,
                      ))
                ],
              ),
            ],
          )),
      Text(
        model.polishedEnglishLabel?.sentencPolishingModel?.data?.explanation ??
            "_",
        style: style_2_28,
      ),
    ];
  }

  Widget _progressWidget(String title, int? socre) {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Row(
        children: [
          Text(
            title,
            style: style_2_28,
          ),
          SizedBox(
            width: 15.w,
          ),
          Text(
            "$socre分",
            style: style_1_28,
          ),
        ],
      ),
      SizedBox(
        height: 8.w,
      ),
      Container(
        height: 20.w,
        width: 240.w,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.w),
            color: const Color(0x1941C0FF)),
        child: FractionallySizedBox(
          alignment: Alignment.centerLeft,
          widthFactor: socre == null ? 0 : socre / 100.0,
          child: Container(
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.w),
                gradient: ColorUtil.blueToGreen),
          ),
        ),
      )
    ]);
  }

  Widget _originTextWidget(BuildContext context) {
    SenceAnalysisModel model = context.read<SenceAnalysisModel>();
    return Container(
      padding: EdgeInsets.fromLTRB(32.w, 24.w, 32.w, 24.w),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24.w), color: Colors.white),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SelectableTextUtil.editableRichText(context,
              textSpan: TextSpan(children: [
                for (int i = 0; i < (soeModel?.data?.words?.length ?? 0); i++)
                  TextSpan(
                    text:
                        "${soeModel?.data?.words![i].word ?? ""}${soeModel?.data?.words![i].rightSymbol ?? " "}",
                    style: TextStyle(
                        color: soeModel?.data?.words![i].color, fontSize: 32.w),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        SelectableTextUtil.showVocabularyAlertWidget(
                            context, soeModel?.data?.words![i].word ?? "");
                      },
                  ),
              ])),
          ..._translateWidgets(context, soeModel?.data?.text),
          SizedBox(
            height: 24.w,
          ),
          _lineWidget(),
          SizedBox(
            height: 24.w,
          ),
          Row(
            children: [
              PlayButtonWidget(
                style: PlayButtonStyle.blue,
                audioUrl: talkModel.audioUrl,
              ),
              SizedBox(
                width: 20.w,
              ),
              GestureDetector(
                  onTap: () {
                    model.translate(soeModel?.data?.text);
                  },
                  child: Image(
                    image: const AssetImage("images/chat_translate_blue.png"),
                    width: 48.w,
                    height: 48.w,
                  )),
              SizedBox(
                width: 20.w,
              ),
              GestureDetector(
                  onTap: () {
                    Clipboard.setData(
                        ClipboardData(text: soeModel?.data?.text ?? ""));
                    EasyLoading.showToast("已复制");
                  },
                  child: Image(
                    image: const AssetImage("images/chat_copy_blue.png"),
                    width: 48.w,
                    height: 48.w,
                  )),
              SizedBox(
                width: 20.w,
              ),
              GestureDetector(
                  onTap: () {
                    model.collection(soeModel?.data?.text);
                  },
                  child: Image(
                    image: AssetImage(
                        model.collectedList.contains(soeModel?.data?.text) ==
                                true
                            ? "images/chat_collected.png"
                            : "images/chat_collect_blue.png"),
                    width: 48.w,
                    height: 48.w,
                  )),
              SizedBox(
                width: 20.w,
              ),
            ],
          ),
        ],
      ),
    );
  }

  List<Widget> _translateWidgets(BuildContext context, String? text) {
    SenceAnalysisModel model = context.read<SenceAnalysisModel>();

    if (model.translateMap[text] == null) {
      return [];
    }
    return [
      SizedBox(
        height: 24.w,
      ),
      Text(
        model.translateMap[text]!,
        style: style_2_24,
      )
    ];
  }

  Row _scoreWidget(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Container(
            height: 48.w,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12.w),
                color: const Color(0xFF41C0FF).withAlpha(25)),
            child: Stack(
              alignment: Alignment.centerLeft,
              children: [
                FractionallySizedBox(
                  alignment: Alignment.topLeft,
                  widthFactor: 0.8,
                  child: Container(
                      height: 48.w,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12.w),
                          color: const Color(0xFF41C0FF))),
                ),
                Positioned(
                    left: 20.w,
                    child: Text(
                      "发音",
                      style: style_1_24,
                    ))
              ],
            ),
          ),
        ),
        SizedBox(
          width: 18.w,
        ),
        Text(
          "90分",
          style: style_1_28,
        )
      ],
    );
  }
}
