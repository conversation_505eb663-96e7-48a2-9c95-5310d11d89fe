import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_app_kouyu/common/http/models/soe_model/soe_model.dart';
import 'package:flutter_app_kouyu/common/utils/common_util.dart';
import 'package:flutter_app_kouyu/widgets/play_button_widget.dart';
import 'package:flutter_app_kouyu/widgets/selected_text.dart';
import 'package:gif_view/gif_view.dart';

class SoeWidget extends StatelessWidget {
  final SoeModel? soeModel;
  const SoeWidget({super.key, required this.soeModel});

  @override
  Widget build(BuildContext context) {
    String? audioUrl;
    if (soeModel?.data?.audioUrl != null) {
      audioUrl = "${soeModel?.data?.audioUrl}?1=1";
    }

    return Container(
        width: double.infinity,
        margin: const EdgeInsetsDirectional.only(start: 20, end: 20, top: 24),
        child: <PERSON><PERSON>(
          children: [
            ListView(
              children: [
                Row(
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.baseline,
                      textBaseline: TextBaseline.alphabetic,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Text(
                          "${soeModel?.data?.suggestedScore ?? "_"}",
                          style: const TextStyle(
                              fontSize: 40,
                              fontWeight: FontWeight.w700,
                              color: Color(0xff00B578)),
                        ),
                        const Text(
                          "分",
                          style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w400,
                              color: Color(0xff00B578)),
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(
                  height: 4,
                ),
                const Text("发音综合评分",
                    style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        color: Color(0xff061B1F))),
                const SizedBox(
                  height: 20,
                ),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: Colors.white,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      scoreWidget("${soeModel?.data?.pronFluency}/100", "流利度"),
                      scoreWidget("${soeModel?.data?.pronAccuracy}/100", "准确度"),
                      scoreWidget(
                          "${soeModel?.data?.pronCompletion}/100", "完整度"),
                      // scoreWidget("_/分钟", "语速"),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 20,
                ),
                const Text("原文",
                    style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        color: Color(0xFF646E70))),
                SelectableTextUtil.editableRichText(context,
                    textSpan: TextSpan(children: [
                      for (int i = 0;
                          i < (soeModel?.data?.words?.length ?? 0);
                          i++)
                        TextSpan(
                          text:
                              "${soeModel?.data?.words![i].word ?? ""}${soeModel?.data?.words![i].rightSymbol ?? " "}",
                          style: TextStyle(
                              color: soeModel?.data?.words![i].color,
                              fontSize: 14),
                          recognizer: TapGestureRecognizer()
                            ..onTap = () {
                              SelectableTextUtil.showVocabularyAlertWidget(
                                  context, soeModel?.data?.words![i].word ?? "");
                            },
                        ),
                    ])),
                const SizedBox(
                  height: 8,
                ),
                Row(
                  children: [
                    GestureDetector(
                      onTap: () {
                        CommonUtils.playVideo(audioUrl);
                      },
                      child: Container(
                          padding: const EdgeInsets.fromLTRB(12, 6, 12, 6),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(5),
                            color: Colors.white,
                          ),
                          child: Text.rich(
                            TextSpan(
                                style: const TextStyle(
                                    color: Color(0xff061B1F),
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400),
                                children: [
                                  const TextSpan(
                                    text: "我的发音",
                                  ),
                                  WidgetSpan(
                                    child: PlayButtonWidget2(
                                      audioUrl: audioUrl,
                                    ),
                                    alignment: PlaceholderAlignment.middle,
                                  ),
                                ]),
                          )),
                    ),
                    const SizedBox(
                      width: 12,
                    ),
                    GestureDetector(
                      onTap: () async {
                        CommonUtils.playTts(soeModel?.data?.text,
                            key: "soe_key1");
                      },
                      child: Container(
                          padding: const EdgeInsets.fromLTRB(12, 6, 12, 6),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(5),
                            color: Colors.white,
                          ),
                          child: Text.rich(
                            TextSpan(
                                style: const TextStyle(
                                    color: Color(0xff061B1F),
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400),
                                children: [
                                  const TextSpan(
                                    text: "标准发音",
                                  ),
                                  WidgetSpan(
                                    child: StreamBuilder<PlayState>(
                                        stream: CommonUtils.playStatus,
                                        builder: (context, snapshot) {
                                          if (CommonUtils.key != "soe_key1" ||
                                              CommonUtils.isPlaying != true) {
                                            return const Image(
                                              image: AssetImage(
                                                  "images/chat_play_black_logo.png"),
                                              width: 18,
                                              height: 18,
                                            );
                                          } else {
                                            return GifView.asset(
                                              "images/chat_play_back.gif",
                                              fit: BoxFit.fill,
                                              width: 18,
                                              height: 18,
                                              repeat: ImageRepeat.repeat,
                                            );
                                          }
                                        }),
                                    alignment: PlaceholderAlignment.middle,
                                  ),
                                ]),
                          )),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 12,
                ),
                Visibility(
                  visible: (soeModel?.data?.words?.length ?? 0) > 0,
                  child: Container(
                      padding: const EdgeInsetsDirectional.all(8),
                      decoration: const BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.all(Radius.circular(12))),
                      child: Column(
                        children: [
                          for (int i = 0;
                              i < (soeModel?.data?.words?.length ?? 0);
                              i++)
                            GestureDetector(
                              onTap: () {
                                CommonUtils.playTts(
                                    soeModel?.data?.words![i].word,
                                    key: "soe$i");
                              },
                              child: Padding(
                                padding: const EdgeInsets.all(8),
                                child: Row(
                                  children: [
                                    Text(
                                      soeModel?.data?.words![i].word ?? "",
                                      style: TextStyle(
                                          color:
                                              soeModel?.data?.words![i].color,
                                          fontSize: 14),
                                    ),
                                    const Spacer(),
                                    StreamBuilder<PlayState>(
                                        stream: CommonUtils.playStatus,
                                        builder: (context, snapshot) {
                                          if (CommonUtils.key != "soe$i" ||
                                              CommonUtils.isPlaying != true) {
                                            return const Image(
                                              image: AssetImage(
                                                  "images/chat_play_black_logo.png"),
                                              width: 18,
                                              height: 18,
                                            );
                                          } else {
                                            return GifView.asset(
                                              "images/chat_play_back.gif",
                                              fit: BoxFit.fill,
                                              width: 18,
                                              height: 18,
                                              repeat: ImageRepeat.repeat,
                                            );
                                          }
                                        }),
                                  ],
                                ),
                              ),
                            ),
                        ],
                      )),
                ),
                const SizedBox(
                  height: 105,
                )
              ],
            ),
            Positioned(
              right: 0,
              top: 0,
              // width: double.infinity,
              child: GestureDetector(
                child: const Icon(Icons.close),
                onTap: () {
                  Navigator.pop(context);
                },
              ),
            )
          ],
        ));
  }

  Widget scoreWidget(String title, String subTitle) {
    return Column(
      children: [
        Text(title),
        Text(
          subTitle,
          style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: Color(0xFF646E70)),
        )
      ],
    );
  }
}
