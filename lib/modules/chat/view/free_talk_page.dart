import 'package:card_swiper/card_swiper.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'dart:io' show Platform;
import 'package:flutter_app_kouyu/common/http/loading_status.dart';
import 'package:flutter_app_kouyu/common/http/models/chat_hint_model/data.dart';
import 'package:flutter_app_kouyu/common/http/models/custome_scene_model/scene_detail_v2_model.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/common/utils/color_util.dart';
import 'package:flutter_app_kouyu/common/utils/common_util.dart';
import 'package:flutter_app_kouyu/common/utils/login_util.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/chat_cubit.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/chat_report_model.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/voice_cubit.dart';
import 'package:flutter_app_kouyu/modules/chat/view/analyze_widget.dart';
import 'package:flutter_app_kouyu/modules/chat/view/chat_report_alert_widget.dart';
import 'package:flutter_app_kouyu/modules/chat/view/chat_report_page.dart';
import 'package:flutter_app_kouyu/modules/chat/view/sence_introduce_widget.dart';
import 'package:flutter_app_kouyu/modules/chat/view/soe_widget.dart';
import 'package:flutter_app_kouyu/modules/chat/view/soe_widget_new.dart';
import 'package:flutter_app_kouyu/modules/chat/view/speed_settings_dialog.dart';
import 'package:flutter_app_kouyu/repository/free_talk_repository.dart';
import 'package:flutter_app_kouyu/widgets/play_button_widget.dart';
import 'package:flutter_app_kouyu/widgets/selected_text.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gif_view/gif_view.dart';
import 'package:provider/provider.dart';

class TalkArgument {
  // 0: 自由聊天 1: 教学模式
  final int model;
  final String topic;
  //soe  analyze     scene 场景编码：当场景为口语测评的时候为oral_assessment，雅思课程传 ielts_course, 托福课程传 toefl_course，自由聊天和场景对话的场景实战传scenario_actual_combat，教学模式传teaching_mode
  //场景编码：自由聊天和场景话题传scene_dialogue,oral_assessment表示口语测评,topic_code对应的多邻国传Duolingo,剑桥少儿英语口语测评传YLE,雅思口语测评传IELTS ielts_mock_test表示雅思模考,topic_code传对应的题目编码  word_learning表示词句学习
  final String uploadFileSence = "scene_dialogue";
  final SceneDetailV2Model? topicDetailModel;

  const TalkArgument(
      {required this.model, this.topic = "", this.topicDetailModel});
}

class FreeTalkPage extends StatelessWidget {
  final TalkArgument argument;
  const FreeTalkPage({super.key, required this.argument});
  static Route<void> route({required TalkArgument argument}) {
    return MaterialPageRoute<void>(
        builder: (_) => FreeTalkPage(
              argument: argument,
            ));
  }

  void pop(BuildContext context) async {
    ChatCubit cubit = context.read<ChatCubit>();
    if (argument.model == 1) {
      //教学模式没有报告
      Navigator.pop(context);
      return;
    }
    if (cubit.state.list.length >= 6) {
      //提示是否生成报告
      int? value = await showModalBottomSheet<int>(
          isScrollControlled: true,
          useSafeArea: true,
          backgroundColor: Colors.transparent,
          constraints: BoxConstraints(maxHeight: 502.w),
          context: context,
          builder: (context) {
            return ChatReportAlert(
              finishTask: cubit.checkTargetFinish,
              hasTask: argument.topic.isNotEmpty,
            );
          });
      if (value == 1) {
        //查看报告
        Navigator.push(
            context,
            MaterialPageRoute<void>(
                builder: (_) => ChatReportPage(
                      conversationId: cubit.state.list.last.conversationId ?? 0,
                      type: ReportDetailType.create,
                    )));
        return;
      }
      Navigator.pop(context);
      return;
    }
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) {
        if (didPop) {
          return;
        }
        pop(context);
      },
      child: MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (context) => VoiceCubit(context),
          ),
          ChangeNotifierProvider(
            create: (context) => ChatCubit(
              context,
              argument: argument,
            )..intiGreetingStream(),
          ),
        ],
        child: Consumer<ChatCubit>(builder: (context, state, _) {
          return Scaffold(
            extendBodyBehindAppBar: true,
            backgroundColor: const Color(0xFFF3FBFD),
            appBar: TalkAPPBar(
              argument: argument,
              finishTask: state.checkTargetFinish,
              conversationId: state.state.list.last.conversationId ?? 0,
              canCreateReport:
                  state.state.list.length >= 6 && argument.model == 0,
              pop: (p0) => pop(context),
            ),
            body: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                FocusScope.of(context).unfocus(); // 点击其他地方收起键盘
              },
              child: Stack(
                children: [
                  Column(
                    children: [
                      Expanded(
                        child: Align(
                          alignment: Alignment.topCenter,
                          child: _chatListView(),
                        ),
                      ),
                      BlocBuilder<VoiceCubit, VoiceState>(
                        builder: (context, state) {
                          return state.voiceType == VoiceTypeEnum.init
                              ? _chatWidget()
                              : VoiceWidget(
                                  model: argument.model,
                                );
                        },
                      )
                    ],
                  ),
                ],
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _chatListView() {
    return Consumer<ChatCubit>(
      builder: (context, state, _) {
        return ListView.builder(
          reverse: true,
          shrinkWrap: true,
          itemBuilder: (BuildContext context, int index) {
            int total = state.state.list.length +
                ((state.state.chatHintStatus == LoadingStatus.inProgress ||
                        state.state.chatHintList.isNotEmpty)
                    ? 1
                    : 0);
            if (index == 0) {
              if (state.state.chatHintList.isNotEmpty) {
                //最后一个且有跟读
                return _followListWidget(state.state.chatHintList);
              } else if (state.state.chatHintStatus ==
                  LoadingStatus.inProgress) {
                return _followListLoadingWidget();
              }
            }

            TalkModel model;
            int chatIndex = total - index - 1;

            model = state.state.list[chatIndex];
            if (model.type == 0) {
              //question
              return Padding(
                padding: EdgeInsets.only(left: 40.w, right: 64.w, bottom: 10.w),
                child: ChatListItemLeft(
                  talkModel: model,
                  index: chatIndex,
                ),
              );
            } else {
              return Padding(
                padding: EdgeInsets.only(left: 64.w, right: 40.w, bottom: 10.w),
                child: ChatListItemRight(
                  talkModel: model,
                  index: chatIndex,
                  argument: argument,
                ),
              );
            }
          },
          itemCount: state.state.list.length +
              ((state.state.chatHintStatus == LoadingStatus.inProgress ||
                      state.state.chatHintList.isNotEmpty)
                  ? 1
                  : 0),
        );
      },
    );
  }

  Widget _followListLoadingWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(
          height: 6,
        ),
        Padding(
          padding: EdgeInsets.only(left: 20),
          child: Text(
            '你可以尝试这样回答${LoginUtil.currentUserInfoModel()?.data?.userSettings?.characterName ?? "小西老师"}',
            style: style_2_28,
          ),
        ),
        SizedBox(
          height: 24.w,
        ),
        Container(
          height: 48,
          width: double.infinity,
          margin: const EdgeInsets.only(left: 20, right: 20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: const BorderRadius.all(Radius.circular(12)),
            border: Border.all(
              color: const Color(0xFF84E9FF),
              width: 3,
            ),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(
                width: 12,
              ),
              GifView.asset(
                "images/hint_loading.gif",
                height: 27,
              ),
            ],
          ),
        ),
        const SizedBox(
          height: 12,
        ),
      ],
    );
  }

  Widget _followListWidget(List<Data> chatHintList) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(
          height: 6,
        ),
        Padding(
          padding: const EdgeInsets.only(left: 20),
          child: Text(
            '你可以尝试这样回答${LoginUtil.currentUserInfoModel()?.data?.userSettings?.characterName ?? "小西老师"}',
            style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w400,
                color: Color(0xFF646E70)),
          ),
        ),
        const SizedBox(
          height: 12,
        ),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: IntrinsicHeight(
            child: Row(
              children: [
                const SizedBox(width: 16),
                ...List.generate(chatHintList.length, (index) {
                  var data = chatHintList[index];
                  return ChatHitItemWidget(
                    hitModel: data,
                  );
                }),
                const SizedBox(width: 16)
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _chatWidget() {
    return BlocBuilder<VoiceCubit, VoiceState>(
      builder: (context, state) {
        return Container(
          padding: EdgeInsets.all(8.w),
          margin: EdgeInsets.only(bottom: 48.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.w),
          ),
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.only(bottom: 16.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      width: 40.w,
                    ),
                    GestureDetector(
                      onTap: () {
                        if (LoadingStatus.inProgress ==
                            context
                                .read<ChatCubit>()
                                .state
                                .list
                                .last
                                .replyStatus
                                ?.status) {
                          EasyLoading.showToast('正在回复...');

                          return;
                        }
                        context
                            .read<VoiceCubit>()
                            .chineseHelp(context.read<ChatCubit>());
                        context.read<ChatCubit>().cleanHit();
                      },
                      child: Container(
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(24.w),
                            border: Border.all(color: separated, width: 2.w)),
                        child: Image.asset(
                          "images/chinese_help_btn.png",
                          width: 199.w,
                          height: 72.w,
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 16.w,
                    ),
                    GestureDetector(
                      onTap: () {
                        context.read<ChatCubit>().getChatHint();
                      },
                      child: Container(
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(24.w),
                            border: Border.all(color: separated, width: 2.w)),
                        child: Image.asset(
                          "images/hit_btn.png",
                          width: 199.w,
                          height: 72.w,
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 40.w,
                    ),
                  ],
                ),
              ),
              _speakWidget(context)
            ],
          ),
        );
      },
    );
  }

  Widget _speakWidget(BuildContext context) {
    return Container(
      height: Platform.isIOS ? 96.w : 96.w,
      margin: EdgeInsets.only(left: 40.w, right: 40.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: const Color(0xFF2693FF),
      ),
      child: TextButton(
          onPressed: () {
            FocusScope.of(context).unfocus(); // 点击其他地方收起键盘

            if (LoadingStatus.inProgress ==
                context.read<ChatCubit>().state.list.last.replyStatus?.status) {
              EasyLoading.showToast('正在回复...');

              return;
            }
            //教学模式支持中英文
            context.read<VoiceCubit>().speakEn(
                isZh: argument.model == 1,
                chatCubit: context.read<ChatCubit>());
            context.read<ChatCubit>().cleanHit();
          },
          style: const ButtonStyle(
            textStyle: MaterialStatePropertyAll(
                TextStyle(fontSize: 20, fontWeight: FontWeight.w700)),
          ),
          child: Row(
            children: [
              if (context.read<VoiceCubit>().showTextInout)
                SizedBox(
                  width: 48.w,
                ),
              const Spacer(),
              Text("点击说话", style: style_1_28.copyWith(color: Colors.white)),
              const Spacer(),
            ],
          )),
    );
  }

  Widget _chatWidget2() {
    return BlocBuilder<VoiceCubit, VoiceState>(
      builder: (context, state) {
        return Container(
          padding: EdgeInsets.all(12.w),
          margin: EdgeInsets.only(bottom: 30.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.w),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                width: 40.w,
              ),
              GestureDetector(
                onTap: () {
                  if (LoadingStatus.inProgress ==
                      context
                          .read<ChatCubit>()
                          .state
                          .list
                          .last
                          .replyStatus
                          ?.status) {
                    EasyLoading.showToast('正在回复...');

                    return;
                  }
                  context
                      .read<VoiceCubit>()
                      .chineseHelp(context.read<ChatCubit>());
                  context.read<ChatCubit>().cleanHit();
                },
                child: Column(
                  children: [
                    Image(
                      image: const AssetImage("images/chat_chinese.png"),
                      width: 64.w,
                      height: 64.w,
                    ),
                    SizedBox(
                      height: 24.w,
                    ),
                    Text(
                      '中文求助',
                      style: style_1_20,
                    )
                  ],
                ),
              ),
              SizedBox(
                width: 40.w,
              ),
              Expanded(
                child: Container(
                  height: Platform.isIOS ? 88.w : 88.w,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: const Color(0xFF2693FF),
                  ),
                  child: TextButton(
                      onPressed: () {
                        FocusScope.of(context).unfocus(); // 点击其他地方收起键盘

                        if (LoadingStatus.inProgress ==
                            context
                                .read<ChatCubit>()
                                .state
                                .list
                                .last
                                .replyStatus
                                ?.status) {
                          EasyLoading.showToast('正在回复...');

                          return;
                        }
                        //教学模式支持中英文
                        context.read<VoiceCubit>().speakEn(
                            isZh: argument.model == 1,
                            chatCubit: context.read<ChatCubit>());
                        context.read<ChatCubit>().cleanHit();
                      },
                      style: const ButtonStyle(
                        textStyle: MaterialStatePropertyAll(TextStyle(
                            fontSize: 20, fontWeight: FontWeight.w700)),
                      ),
                      child: Center(
                        child: Text("点击说话",
                            style: style_1_28.copyWith(color: Colors.white)),
                      )),
                ),
              ),
              SizedBox(
                width: 40.w,
              ),
              GestureDetector(
                onTap: () {
                  context.read<ChatCubit>().getChatHint();
                },
                child: Column(
                  children: [
                    Image(
                      image: const AssetImage("images/chat_tishi.png"),
                      width: 64.w,
                      height: 64.w,
                    ),
                    SizedBox(
                      height: 24.w,
                    ),
                    Text(
                      '对话提示',
                      style: style_1_20,
                    )
                  ],
                ),
              ),
              SizedBox(
                width: 40.w,
              ),
            ],
          ),
        );
      },
    );
  }
}

class ChatHitItemWidget extends StatelessWidget {
  final Data? hitModel;
  const ChatHitItemWidget({super.key, required this.hitModel});

  @override
  Widget build(BuildContext context) {
    return Container(
        width: 300,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: const BorderRadius.all(Radius.circular(12)),
          border: Border.all(
            color: const Color(0xFF84E9FF),
            width: 3.w,
          ),
        ),
        padding:
            const EdgeInsets.only(left: 12, right: 12, top: 12, bottom: 12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Flexible(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    hitModel?.english ?? "",
                    style:
                        TextStyle(fontWeight: FontWeight.w400, fontSize: 28.w),
                  ),
                  SizedBox(height: 8.w),
                  Text(
                    hitModel?.chinese ?? "",
                    style: Platform.isIOS
                        ? style_2_24
                        : style_2_24.copyWith(fontSize: 20.w),
                  ),
                ],
              ),
            ),
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(height: 8.w),
                _lineWidget(),
                SizedBox(height: 16.w),
                Row(
                  children: [
                    PlayButtonWidget(
                        audioUrl: hitModel?.audioUrl ?? "",
                        style: PlayButtonStyle.blue),
                    SizedBox(width: 20.w),
                    GestureDetector(
                        onTap: () {
                          context
                              .read<ChatCubit>()
                              .collectionHitModel(hitModel!);
                        },
                        child: Image(
                          image: AssetImage(hitModel!.collected == true
                              ? "images/chat_collected.png"
                              : "images/chat_collect_blue.png"),
                          width: 48.w,
                          height: 48.w,
                        )),
                    SizedBox(width: 20.w),
                    GestureDetector(
                      onTap: () {
                        Clipboard.setData(
                            ClipboardData(text: hitModel?.english ?? ""));
                        EasyLoading.showToast("已复制");
                      },
                      child: Image(
                        image: const AssetImage("images/chat_copy_blue.png"),
                        width: 48.w,
                        height: 48.w,
                      ),
                    ),
                    const Spacer(),
                    GestureDetector(
                      onTap: () {
                        context.read<VoiceCubit>().followRead(
                            hitModel?.english, context.read<ChatCubit>());
                        context.read<ChatCubit>().cleanHit();
                      },
                      child: Image.asset(
                        "images/fllow_btn.png",
                        height: 28,
                      ),
                    ),
                  ],
                ),
              ],
            )
          ],
        ));
  }
}

class ChatListItemLeft extends StatelessWidget {
  final TalkModel? talkModel;
  final int index;
  const ChatListItemLeft(
      {super.key, required this.talkModel, required this.index});

  @override
  Widget build(BuildContext context) {
    if (talkModel?.replyStatus?.status == LoadingStatus.inProgress) {
      return Container(
          margin: const EdgeInsets.only(top: 6, bottom: 6),
          padding:
              const EdgeInsets.only(left: 12, right: 12, top: 12, bottom: 12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: const Color(0xFFDCF4FA),
          ),
          child: Row(
            children: [
              GifView.asset(
                "images/reply_loading.gif",
                fit: BoxFit.fill,
                width: 36,
                height: 27,
                repeat: ImageRepeat.repeat,
              ),
            ],
          ));
    }
    return Container(
        margin: const EdgeInsets.only(top: 6, bottom: 0),
        padding:
            const EdgeInsets.only(left: 12, right: 12, top: 12, bottom: 12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: const Color(0xFFDCF4FA),
        ),
        child: talkModel?.replyStatus?.status == LoadingStatus.inProgress
            ? GifView.asset(
                "images/reply_loading.gif",
                fit: BoxFit.fitWidth,
                width: 16,
                height: 16,
                repeat: ImageRepeat.repeat,
              )
            : Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SelectableTextUtil.editableText(
                    context,
                    text: talkModel?.question ?? "",
                    style: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: Platform.isIOS ? 16 : 14),
                  ),
                  talkModel?.translateStatus?.status == LoadingStatus.inProgress
                      ? GifView.asset(
                          "images/samll_loading_blue.gif",
                          fit: BoxFit.fitWidth,
                          width: 16,
                          height: 16,
                          repeat: ImageRepeat.repeat,
                        )
                      : Visibility(
                          visible: talkModel?.translateText != null &&
                              talkModel!.translateText!.isNotEmpty,
                          child: Text(
                            talkModel?.translateText ?? "",
                            style: TextStyle(
                                fontWeight: FontWeight.w400,
                                fontSize: Platform.isIOS ? 12 : 10,
                                color: Color(0xFF646E70)),
                          ),
                        ),
                  SizedBox(
                    height: 24.w,
                  ),
                  _lineWidget(),
                  SizedBox(
                    height: 24.w,
                  ),
                  Row(
                    children: [
                      PlayButtonWidget(
                          audioUrl: talkModel?.audioUrl ?? "",
                          style: PlayButtonStyle.blue),
                      SizedBox(
                        width: 20.w,
                      ),
                      GestureDetector(
                          onTap: () {
                            if (talkModel?.translateText == null ||
                                talkModel!.translateText!.isEmpty) {
                              context
                                  .read<ChatCubit>()
                                  .translate(talkModel!, index);
                            }
                          },
                          child: Image(
                            image: const AssetImage(
                                "images/chat_translate_blue.png"),
                            width: 48.w,
                            height: 48.w,
                          )),
                      SizedBox(
                        width: 20.w,
                      ),
                      Visibility(
                        //教学模式不显示收藏
                        visible: context.read<ChatCubit>().argument.model != 1,
                        child: GestureDetector(
                            onTap: () {
                              FocusScope.of(context).unfocus(); // 点击其他地方收起键盘

                              context
                                  .read<ChatCubit>()
                                  .collection(talkModel!, index);
                            },
                            child: Image(
                              image: AssetImage(talkModel!.collected == true
                                  ? "images/chat_collected.png"
                                  : "images/chat_collect_blue.png"),
                              width: 48.w,
                              height: 48.w,
                            )),
                      ),
                      Visibility(
                        visible: context.read<ChatCubit>().argument.model != 1,
                        child: SizedBox(
                          width: 20.w,
                        ),
                      ),
                      GestureDetector(
                          onTap: () {
                            Clipboard.setData(
                                ClipboardData(text: talkModel?.question ?? ""));
                            EasyLoading.showToast("已复制");
                          },
                          child: Image(
                            image:
                                const AssetImage("images/chat_copy_blue.png"),
                            width: 48.w,
                            height: 48.w,
                          )),
                      SizedBox(
                        width: 20.w,
                      ),
                    ],
                  ),
                ],
              ));
  }
}

Container _lineWidget({double? padding}) {
  return Container(
    height: 1.w,
    margin: EdgeInsets.only(left: padding ?? 0, right: padding ?? 0),
    width: double.infinity,
    color: ColorUtil.separated,
  );
}

class ChatListItemRight extends StatelessWidget {
  final TalkModel talkModel;
  final int index;
  final TalkArgument argument;

  const ChatListItemRight(
      {super.key,
      required this.talkModel,
      required this.index,
      required this.argument});

  @override
  Widget build(
    BuildContext context,
  ) {
    return Container(
        margin: const EdgeInsets.only(top: 6, bottom: 6),
        padding:
            const EdgeInsets.only(left: 12, right: 12, top: 12, bottom: 12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.white,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SelectableTextUtil.editableText(
              context,
              text: talkModel.question ?? "",
              style: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontSize: Platform.isIOS ? 16 : 14),
            ),
            SizedBox(
              height: 24.w,
            ),
            _lineWidget(),
            SizedBox(
              height: 24.w,
            ),
            Row(
              children: [
                PlayButtonWidget(
                    audioUrl: talkModel.audioUrl ?? "",
                    style: PlayButtonStyle.blue),
                SizedBox(
                  width: 20.w,
                ),
                PlayButtonWidget(
                  text: talkModel.question,
                  style: PlayButtonStyle.blue,
                  image: Image(
                    image: const AssetImage("images/chat_ai_blue.png"),
                    width: 48.w,
                    height: 48.w,
                  ),
                ),
                SizedBox(
                  width: 20.w,
                ),
                GestureDetector(
                    onTap: () {
                      FocusScope.of(context).unfocus(); // 点击其他地方收起键盘

                      context.read<ChatCubit>().collection(talkModel!, index);
                    },
                    child: Image(
                      image: AssetImage(talkModel!.collected == true
                          ? "images/chat_collected.png"
                          : "images/chat_collect_blue.png"),
                      width: 48.w,
                      height: 48.w,
                    )),
                SizedBox(
                  width: 20.w,
                ),
                GestureDetector(
                  onTap: () {
                    FocusScope.of(context).unfocus(); // 点击其他地方收起键盘

                    Clipboard.setData(
                        ClipboardData(text: talkModel?.question ?? ""));
                    EasyLoading.showToast("已复制");
                  },
                  child: Image(
                    image: const AssetImage("images/chat_copy_blue.png"),
                    width: 48.w,
                    height: 48.w,
                  ),
                ),
                const Spacer(),
                GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    if (talkModel.soeModel == null) {
                      return;
                    }
                    showModalBottomSheet(
                        isScrollControlled: true,
                        useSafeArea: true,
                        constraints: BoxConstraints(
                            maxHeight: MediaQuery.of(context).size.height -
                                MediaQuery.of(context).padding.top -
                                140),
                        shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(20),
                                topRight: Radius.circular(20))),
                        context: context,
                        builder: (context) {
                          return SoeWidgetNew(
                            soeModel: talkModel.soeModel,
                            analyzeModel: talkModel.analyzeModel,
                            talkModel: talkModel,
                            argument: argument,
                          );
                        });
                  },
                  child: Row(
                    children: [
                      Visibility(
                        visible: (talkModel.analyzeStatus?.status !=
                                    LoadingStatus.failure ||
                                talkModel.analyzeStatus?.message != null) &&
                            argument.model == 0,
                        child: Row(
                          children: [
                            if (talkModel.analyzeStatus?.status !=
                                LoadingStatus.inProgress)
                              (talkModel.analyzeModel?.data?.correct == false ||
                                      talkModel.analyzeStatus?.status ==
                                          LoadingStatus.failure)
                                  ? Image.asset(
                                      "images/chat_no_good.png",
                                      width: 16,
                                      height: 16,
                                    )
                                  : Image.asset(
                                      "images/chat_good_logo.png",
                                      width: 16,
                                      height: 16,
                                    ),
                            const SizedBox(
                              width: 2,
                            ),
                            talkModel.analyzeStatus?.status ==
                                    LoadingStatus.inProgress
                                ? GifView.asset(
                                    "images/samll_loading.gif",
                                    fit: BoxFit.fitWidth,
                                    width: 16,
                                    height: 16,
                                    repeat: ImageRepeat.repeat,
                                  )
                                : Text(
                                    talkModel.analyzeStatus?.message ??
                                        (talkModel.analyzeModel?.data
                                                    ?.correct ==
                                                true
                                            ? "语法正确"
                                            : "语法错误"),
                                    style: TextStyle(
                                        fontSize: Platform.isIOS ? 12 : 10,
                                        fontWeight: FontWeight.w400,
                                        color: Color(0xff646E70)),
                                  ),
                          ],
                        ),
                      ),
                      SizedBox(
                        width: 16.w,
                      ),
                      Visibility(
                        visible: talkModel.soeStatus?.status !=
                                LoadingStatus.failure ||
                            talkModel.soeStatus?.message != null,
                        child: Row(
                          children: [
                            if (talkModel!.soeStatus?.status !=
                                LoadingStatus.inProgress)
                              ((talkModel.soeModel?.data?.suggestedScore ??
                                              100) <
                                          80 ||
                                      talkModel.soeStatus?.status ==
                                          LoadingStatus.failure)
                                  ? Image.asset(
                                      "images/chat_no_good.png",
                                      width: 16,
                                      height: 16,
                                    )
                                  : Image.asset(
                                      "images/chat_good_logo.png",
                                      width: 16,
                                      height: 16,
                                    ),
                            const SizedBox(
                              width: 2,
                            ),
                            talkModel!.soeStatus?.status ==
                                    LoadingStatus.inProgress
                                ? GifView.asset(
                                    "images/samll_loading.gif",
                                    fit: BoxFit.fitWidth,
                                    width: 16,
                                    height: 16,
                                    repeat: ImageRepeat.repeat,
                                  )
                                : Text(
                                    talkModel.soeModel?.data?.suggestedScore !=
                                            null
                                        ? "发音${talkModel.soeModel?.data?.suggestedScore}分"
                                        : talkModel.soeStatus?.message ?? "",
                                    style: TextStyle(
                                        fontSize: Platform.isIOS ? 12 : 10,
                                        fontWeight: FontWeight.w400,
                                        color: Color(0xff646E70)),
                                  ),
                            Image.asset(
                              "images/right_arrow_gray.png",
                              width: 36.w,
                              height: 36.w,
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                )
              ],
            ),
          ],
        ));
  }
}

class VoiceWidget extends StatelessWidget {
  // model 0: 自由聊天 1: 教学模式
  final int model;
  const VoiceWidget({
    super.key,
    required this.model,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<VoiceCubit, VoiceState>(
      builder: (context, state) {
        WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
          ScrollController scrollController =
              context.read<VoiceCubit>().scrollController;
          scrollController.jumpTo(scrollController.position.maxScrollExtent);
        });
        final maxSeconds = context.read<VoiceCubit>().maxSeconds;
        return Offstage(
          offstage: state.voiceType == VoiceTypeEnum.init,
          child: Stack(
            alignment: Alignment.topCenter,
            children: [
              Column(
                children: [
                  Visibility(
                    visible: state.voiceType == VoiceTypeEnum.follow,
                    child: Container(
                      padding: Platform.isIOS
                          ? const EdgeInsets.fromLTRB(12, 40, 12, 40)
                          : const EdgeInsets.fromLTRB(12, 30, 12, 30),
                      margin: Platform.isIOS
                          ? const EdgeInsets.fromLTRB(30, 0, 40, 23)
                          : const EdgeInsets.fromLTRB(30, 0, 40, 18),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        gradient: const LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              Color(0xFF98ECEC),
                              Color(0xFF84E9FF),
                              Color(0xFF9BE1FF)
                            ]),
                      ),
                      child: Center(
                        child: Text(state.followTest ?? "_",
                            style: TextStyle(
                                color: const Color(0xFF061B1F),
                                fontSize: Platform.isIOS ? 16 : 14,
                                fontWeight: FontWeight.w400)),
                      ),
                    ),
                  ),
                  Container(
                    color: Colors.transparent,
                    height: 2.w,
                    width: double.infinity,
                    child: Center(
                      child: LinearProgressIndicator(
                        value: state.seconds / maxSeconds,
                        minHeight: 2.w,
                        backgroundColor: Colors.white,
                        color: Colors.cyan,
                      ),
                    ),
                  ),
                  Container(
                    padding:
                        const EdgeInsets.only(left: 20, right: 20, top: 12),
                    color: Colors.white,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        TextField(
                          maxLines: Platform.isIOS ? 5 : 4,
                          readOnly: true,
                          controller:
                              context.read<VoiceCubit>().textEditingController,
                          scrollController:
                              context.read<VoiceCubit>().scrollController,
                          style: TextStyle(
                              fontSize: Platform.isIOS ? 16 : 14,
                              fontWeight: FontWeight.w400,
                              color: const Color(0xFF061B1F)),
                          textAlign: TextAlign.center,
                          decoration: const InputDecoration(
                            border: InputBorder.none,
                            enabledBorder: InputBorder.none,
                            focusedBorder: InputBorder.none,
                          ),
                        ),
                        Visibility(
                            visible:
                                state.voiceType == VoiceTypeEnum.cnTanslated,
                            child: const SizedBox(height: 10)),
                        Visibility(
                            visible:
                                state.voiceType == VoiceTypeEnum.cnTanslated,
                            child: Align(
                              alignment: Alignment.center,
                              child: GestureDetector(
                                onTap: () => CommonUtils.playVideo(state
                                    .chToEnAudioModel?.data?.replyAudioUrl),
                                child: RichText(
                                    maxLines: 3,
                                    text: TextSpan(
                                        text: state.chToEnAudioModel?.data
                                                ?.reply ??
                                            "",
                                        style: TextStyle(
                                            color: const Color(0xFF061B1F),
                                            fontWeight: FontWeight.w400,
                                            fontSize: Platform.isIOS ? 16 : 14),
                                        children: [
                                          WidgetSpan(
                                              alignment:
                                                  PlaceholderAlignment.middle,
                                              child: PlayButtonWidget(
                                                style: PlayButtonStyle.white,
                                                audioUrl: state.chToEnAudioModel
                                                    ?.data?.replyAudioUrl,
                                              )),
                                        ])),
                              ),
                            )),
                        const SizedBox(height: 20),
                        Visibility(
                          visible: state.voiceType == VoiceTypeEnum.cnTanslated,
                          child: Container(
                            height: Platform.isIOS ? 52 : 45,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              gradient: const LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    Color(0xFF98ECEC),
                                    Color(0xFF84E9FF),
                                    Color(0xFF9BE1FF)
                                  ]),
                            ),
                            child: TextButton(
                                onPressed: () {
                                  FocusScope.of(context)
                                      .unfocus(); // 点击其他地方收起键盘

                                  context.read<VoiceCubit>().followRead(
                                      state.chToEnAudioModel?.data?.reply,
                                      context.read<ChatCubit>());
                                  context.read<ChatCubit>().cleanHit();
                                },
                                style: const ButtonStyle(
                                  textStyle: MaterialStatePropertyAll(TextStyle(
                                      fontSize: 20,
                                      fontWeight: FontWeight.w700)),
                                ),
                                child: Center(
                                  child: Text("点击跟读",
                                      style: TextStyle(
                                          color: const Color(0xFF061B1F),
                                          fontSize: Platform.isIOS ? 18 : 16,
                                          fontWeight: FontWeight.w800)),
                                )),
                          ),
                        ),
                        Visibility(
                          visible: state.voiceType != VoiceTypeEnum.cnTanslated,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              const SizedBox(
                                width: 20,
                              ),
                              GestureDetector(
                                onTap: () {
                                  context.read<VoiceCubit>().stop();
                                },
                                child: const Image(
                                  image: AssetImage("images/chat_close.png"),
                                  width: 40,
                                  height: 40,
                                ),
                              ),
                              const SizedBox(
                                width: 20,
                              ),
                              Expanded(
                                child: GifView.asset(
                                  "images/chat_loading.gif",
                                  fit: BoxFit.cover,
                                  width: double.infinity,
                                  height: Platform.isIOS ? 36 : 30,
                                  repeat: ImageRepeat.repeat,
                                ),
                              ),
                              const SizedBox(
                                width: 20,
                              ),
                              GestureDetector(
                                onTap: () {
                                  context
                                      .read<VoiceCubit>()
                                      .action(context.read<ChatCubit>());
                                },
                                child: Image(
                                  image: state.voiceType == VoiceTypeEnum.ch
                                      ? const AssetImage(
                                          "images/chat_translate_big.png")
                                      : const AssetImage(
                                          "images/chat_send.png"),
                                  width: 40,
                                  height: 40,
                                ),
                              ),
                              const SizedBox(
                                width: 20,
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                          height: Platform.isIOS ? 70.w : 60.w,
                        )
                      ],
                    ),
                  )
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}

class Sky extends CustomPainter {
  final double progress;
  static double pi = 3.1415926;
  final Paint _paint = Paint()
    ..color = Colors.white
    ..isAntiAlias = true
    ..style = PaintingStyle.fill //绘画风格，默认为填充
    ..strokeWidth = 2.0;

  final Paint _paint2 = Paint()
    ..color = Colors.cyan //画笔颜色
    ..isAntiAlias = true
    ..style = PaintingStyle.stroke //绘画风格，默认为填充
    ..strokeWidth = 2.0;

  Sky({super.repaint, required this.progress});
  @override
  void paint(Canvas canvas, Size size) {
    Rect rect = Rect.fromLTRB(0, 0, size.width, 44);
    canvas.drawArc(rect, pi, pi, false, _paint);
    rect = Rect.fromLTRB(0, 0, size.width, 44 - 2);

    canvas.drawArc(rect, pi, pi * progress, false, _paint2);
  }

  @override
  bool shouldRepaint(Sky oldDelegate) => true;
  @override
  bool shouldRebuildSemantics(Sky oldDelegate) => false;
}

class TalkAPPBar extends StatelessWidget implements PreferredSizeWidget {
  const TalkAPPBar(
      {super.key,
      required this.argument,
      required this.pop,
      required this.conversationId,
      required this.canCreateReport,
      required this.finishTask});
  final TalkArgument argument;
  final num conversationId;
  final bool canCreateReport;
  final bool finishTask;

  final Function(BuildContext) pop;

  @override
  Size get preferredSize => Size.fromHeight(248.w);

  @override
  Widget build(BuildContext context) {
    return Container(
        decoration: const BoxDecoration(
          color: Colors.transparent,
          image: DecorationImage(
              alignment: Alignment.topCenter,
              image: AssetImage("images/free_talk_top_bg.png"),
              fit: BoxFit.fill),
        ),
        child: SafeArea(
          child: Column(
            children: [
              Row(
                mainAxisSize: MainAxisSize.max,
                children: [
                  const SizedBox(
                    width: 17,
                  ),
                  GestureDetector(
                    onTap: () {
                      pop(context);
                    },
                    child: Image.asset(
                      "images/navigation_back.png",
                      width: 24,
                      height: 24,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    argument.topicDetailModel?.data?.chinese ?? "自由聊天",
                    style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w800,
                        color: Color(0xFF061B1F)),
                  ),
                  SizedBox(
                    width: 16.w,
                  ),
                  Container(
                      height: 40.w,
                      width: 60.w,
                      decoration: BoxDecoration(
                        borderRadius:
                            const BorderRadius.all(Radius.circular(12)),
                        border: Border.all(
                          color: const Color(0xFFDDEDF0),
                          width: 1,
                        ),
                      ),
                      child: Center(
                          child: Text(
                        argument.model == 1 ? "教学" : "实战",
                        style: style_1_20,
                      ))),
                  const Spacer(),
                  const SizedBox(
                    width: 41,
                  ),
                ],
              ),
              SizedBox(
                height: 32.w,
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(
                    width: 17,
                  ),
                  Image.network(
                    LoginUtil.currentUserInfoModel()
                            ?.data
                            ?.userSettings
                            ?.iconUrl ??
                        "_",
                    fit: BoxFit.contain,
                    width: 40,
                    height: 40,
                  ),
                  const SizedBox(
                    width: 10,
                  ),
                  Text(
                    LoginUtil.currentUserInfoModel()
                            ?.data
                            ?.userSettings
                            ?.characterName ??
                        "小西老师",
                    style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w800,
                        color: Color(0xFF061B1F)),
                  ),
                  const Spacer(),
                  Column(
                    children: [
                      GestureDetector(
                          onTap: () {
                            FocusScope.of(context).unfocus(); // 点击其他地方收起键盘

                            _showTask(context);
                          },
                          child: Visibility(
                              visible: argument.topicDetailModel != null,
                              child: Image.asset(
                                finishTask
                                    ? "images/chat_task_success.png"
                                    : "images/chat_task.png",
                                width: 64.w,
                                height: 64.w,
                              ))),
                    ],
                  ),
                  SizedBox(
                    width: 16.w,
                  ),
                  Visibility(
                    //话题任务的实战切未完成任务才展示
                    visible: argument.model == 0 && argument.topic.isNotEmpty,
                    child: GestureDetector(
                        onTap: () {
                          FocusScope.of(context).unfocus(); // 点击其他地方收起键盘

                          if (!finishTask) {
                            EasyLoading.showToast("您还未完成任务哦~");
                          } else {
                            //查看报告
                            Navigator.push(
                                context,
                                MaterialPageRoute<void>(
                                    builder: (_) => ChatReportPage(
                                          conversationId: conversationId,
                                          type: ReportDetailType.create,
                                        )));
                          }
                        },
                        child: Visibility(
                            visible: argument.topicDetailModel != null,
                            child: Image.asset(
                              finishTask
                                  ? "images/chat_finish.png"
                                  : "images/chat_unfinish.png",
                              width: 64.w,
                              height: 64.w,
                            ))),
                  ),
                  Visibility(
                      visible: argument.model == 0 && argument.topic.isNotEmpty,
                      child: SizedBox(
                        width: 16.w,
                      )),
                  GestureDetector(
                      onTap: () {
                        FocusScope.of(context).unfocus(); // 点击其他地方收起键盘

                        _showSettingAlert(context);
                      },
                      child: Image.asset(
                        "images/chat_setting.png",
                        width: 64.w,
                        height: 64.w,
                      )),
                  const SizedBox(
                    width: 17,
                  ),
                ],
              ),
              Visibility(
                visible: argument.topicDetailModel != null && finishTask,
                child: Row(
                  children: [
                    const Spacer(),
                    GestureDetector(
                      onTap: () {
                        FocusScope.of(context).unfocus(); // 点击其他地方收起键盘

                        Navigator.push(
                            context,
                            MaterialPageRoute<void>(
                                builder: (_) => ChatReportPage(
                                      conversationId: conversationId,
                                      type: ReportDetailType.create,
                                    )));
                        ;
                      },
                      child: Image.asset(
                        "images/chat_task_bottom.png",
                        width: 245.w,
                        height: 54.w,
                      ),
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                  ],
                ),
              )
            ],
          ),
        ));
  }

  _showSettingAlert(BuildContext context) {
    showDialog(
        context: context,
        barrierColor: Colors.transparent,
        routeSettings: const RouteSettings(arguments: {"stopPlay": false}),
        builder: (context) {
          return GestureDetector(
              onTap: () {
                Navigator.pop(context);
              },
              child: Container(
                  width: double.infinity,
                  height: double.infinity,
                  color: Colors.transparent,
                  child: Align(
                      alignment: Alignment.topRight,
                      child: Container(
                          margin: EdgeInsets.only(right: 40.w, top: 170.w),
                          width: 247.w,
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(32.w)),
                          child: GestureDetector(
                            onTap: () {
                              Navigator.pop(context);
                              _showSpeechSetting(context);
                            },
                            child: SizedBox(
                              height: 88.w,
                              width: double.infinity,
                              child: Row(
                                children: [
                                  const Spacer(),
                                  Image.asset(
                                    "images/sppech_setting.png",
                                    width: 36.w,
                                    height: 36.w,
                                  ),
                                  SizedBox(
                                    width: 12.w,
                                  ),
                                  Text(
                                    "语速设置",
                                    style: style_1_28_400,
                                  ),
                                  const Spacer(),
                                ],
                              ),
                            ),
                          )))));
        });
  }

  _showSpeechSetting(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      constraints: BoxConstraints(maxWidth: double.infinity, maxHeight: 558.w),
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20), topRight: Radius.circular(20))),
      builder: (context) {
        return const SpeedSettingsDialog();
      },
    );
  }

  _showTask(BuildContext context) {
    showModalBottomSheet(
        backgroundColor: Colors.transparent,
        constraints:
            BoxConstraints(maxWidth: double.infinity, maxHeight: 900.w),
        isScrollControlled: true,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20), topRight: Radius.circular(20))),
        context: context,
        builder: (context) {
          return Padding(
            padding: EdgeInsets.only(left: 24.w, right: 24.w, bottom: 50.w),
            child: Container(
              clipBehavior: Clip.antiAlias,
              decoration:
                  BoxDecoration(borderRadius: BorderRadius.circular(40.w)),
              child: SenceIntroduceWidget(
                topicDetail: argument.topicDetailModel!,
                canCreateReport: canCreateReport,
                conversationId: conversationId,
              ),
            ),
          );
        });
  }
}
