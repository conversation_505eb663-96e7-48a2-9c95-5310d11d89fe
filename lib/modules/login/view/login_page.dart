import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/utils/login_util.dart';
import 'package:flutter_app_kouyu/modules/login/bloc/login_bloc.dart';
import 'package:flutter_app_kouyu/modules/login/view/login_form.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:formz/formz.dart';

class LoginPage extends StatelessWidget {
  const LoginPage({super.key});
  static Route<void> route() {
    return MaterialPageRoute<void>(builder: (_) => const LoginPage());
  }

  @override
  Widget build(BuildContext context) {
    LoginBloc bloc = LoginBloc();
    return BlocProvider(
      create: (_) {
        return bloc;
      },
      child: BlocListener<LoginBloc, LoginState>(
        listener: (context, state) {
          if (state.status.isInitial) {
            EasyLoading.dismiss();
          }
          if (state.status.isFailure) {
            EasyLoading.showError("登录失败");
          }
          if (state.status.isInProgress) {
            EasyLoading.show(status: "登录中");
          }
          if (state.status.isSuccess) {
            EasyLoading.showSuccess("登录成功");
            if (state.loginInfoModel?.data?.enterTheProcess == true) {
              //去设置
              Navigator.of(context).pushNamedAndRemoveUntil(
                  "/setup_setting_one", (route) => false);
            } else {
              LoginUtil.checkAppVersion = true;
              Navigator.of(context)
                  .pushNamedAndRemoveUntil("/home", (route) => false);
            }
          }
        },
        child: Scaffold(
            resizeToAvoidBottomInset: false, //输入框抵住键盘
            extendBodyBehindAppBar: true,
            appBar: AppBar(
              leadingWidth: 300,
              elevation: 0, //消除阴影
              actions: Platform.isIOS ? [
                GestureDetector(
                  onTap: () {
                    bloc.add(const VisitorLoginSubmitted());
                  },
                  child: const Text(
                    "游客模式",
                    style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        color: Color(0xFF646E70)),
                  ),
                ),
                const SizedBox(
                  width: 12,
                )
              ] : [],
              backgroundColor: Colors.transparent,

              // leading: CustomAppbar.leftWidget(
              //   context,
              //   text: "",
              //   ontap: () {
              //     if (Navigator.of(context).canPop()) {
              //       Navigator.of(context).pop();
              //     } else {
              //       Navigator.of(context)
              //           .pushNamedAndRemoveUntil("/home", (route) => false);
              //     }
              //   },
              // ),
            ),
            backgroundColor: Colors.white,
            body: LoginForm()),
      ),
    );
  }
}
