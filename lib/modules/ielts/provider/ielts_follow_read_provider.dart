// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'package:flutter_easyloading/flutter_easyloading.dart';

import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/audio_upload_model/audio_upload_model.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_speaking_soe_model/ielts_speaking_soe_model.dart';
import 'package:flutter_app_kouyu/common/http/models/scene_topics_vocabulary_model/scene_topics_vocabulary_model.dart';
import 'package:flutter_app_kouyu/common/http/models/sence_topic_sentence/sence_topic_sentence.dart';
import 'package:flutter_app_kouyu/common/provider/voice_model.dart';

class IeltsFollowReadProvider extends VoiceModel {
  IeltsSpeakingSoeModel? soeModel;
  int practiceId;
  int questionId;
  String text;
  IeltsFollowReadProvider(
      {this.soeModel,
      required this.practiceId,
      required this.questionId,
      required this.text,
      required super.buildContext});

  @override
  void dispose() {
    stopRecording();
    super.dispose();
  }

  Future<bool> sent() async {
    await super.stop();
    notifyListeners();
    EasyLoading.show(status: "测评中...");
    try {
      //上传文件
      AudioUploadModel audioUploadModel =
          await Api.uploadFile(filePath, topicCode: "", scene: "");
      //soe
      soeModel = await Api.ieltsSpeakingSoe(
          practiceId, questionId, text, audioUploadModel.data?.fileUrl ?? "");
    } finally {
      EasyLoading.dismiss();
    }

    notifyListeners();

    return true;
  }

  followFinish() {}
}
