import 'package:flutter_app_kouyu/common/http/models/collection_collocation_detail/collection_collocation_detail.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/audio_upload_model/audio_upload_model.dart';
import 'package:flutter_app_kouyu/common/http/models/soe_model/soe_model.dart';
import 'package:flutter_app_kouyu/common/provider/voice_model.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/voice_cubit.dart';

class CollectedCollocationDetailProvider extends VoiceModel {
  VoiceStateEnum voiceState = VoiceStateEnum.init;
  final int collocationId;
  CollectionCollocationDetail? collocationDetail;
  SoeModel? soeModel;
  CollectedCollocationDetailProvider(
      {required this.collocationId, required super.buildContext});

  refreshData() async {
    collocationDetail = await Api.getCollocationDetail(collocationId);
    notifyListeners();
  }

  Future<bool> sent() async {
    await super.stop();
    voiceState = VoiceStateEnum.init;
    notifyListeners();

    // 获取短语动词文本用于发音评测
    String collocationText = collocationDetail?.data?.collocation ?? "";
    if (collocationText.isEmpty) {
      return false;
    }

    //上传文件
    AudioUploadModel audioUploadModel =
        await Api.uploadFile(filePath, topicCode: "", scene: "word_learning");
    //soe
    soeModel = await Api.soe({
      "text": collocationText,
      "audio_url": audioUploadModel.data?.fileUrl ?? "",
      "client_sentence_id": 0,
      "conversation_id": 0,
      "scene": "",
    });

    if (soeModel?.data?.suggestedScore != null) {
      collocationDetail?.data?.score = "${soeModel?.data?.suggestedScore}";
      collocationDetail?.data?.userPronunciationUrl =
          audioUploadModel.data?.fileUrl;
    }

    notifyListeners();

    return true;
  }
}
