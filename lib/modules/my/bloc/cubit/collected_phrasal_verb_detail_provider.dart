import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/audio_upload_model/audio_upload_model.dart';
import 'package:flutter_app_kouyu/common/http/models/collection_phrasal_verb_detail/collection_phrasal_verb_detail.dart';
import 'package:flutter_app_kouyu/common/http/models/soe_model/soe_model.dart';
import 'package:flutter_app_kouyu/common/provider/voice_model.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/voice_cubit.dart';

class CollectedPhrasalVerbDetailProvider extends VoiceModel {
  VoiceStateEnum voiceState = VoiceStateEnum.init;

  final int phrasalVerbId;
  CollectionPhrasalVerbDetail? phrasalVerbDetail;
  CollectedPhrasalVerbDetailProvider(
      {required this.phrasalVerbId, required super.buildContext});
  SoeModel? soeModel;

  refreshData() async {
    phrasalVerbDetail = await Api.getPhrasalVerbDetail(phrasalVerbId);
    notifyListeners();
  }

  Future<bool> sent() async {
    await super.stop();
    voiceState = VoiceStateEnum.init;
    notifyListeners();

    // 获取短语动词文本用于发音评测
    String phrasalVerbText = phrasalVerbDetail?.data?.phrasalVerb ?? "";
    if (phrasalVerbText.isEmpty) {
      return false;
    }

    //上传文件
    AudioUploadModel audioUploadModel =
        await Api.uploadFile(filePath, topicCode: "", scene: "word_learning");
    //soe
    soeModel = await Api.soe({
      "text": phrasalVerbText,
      "audio_url": audioUploadModel.data?.fileUrl ?? "",
      "client_sentence_id": 0,
      "conversation_id": 0,
      "scene": "",
    });

    if (soeModel?.data?.suggestedScore != null) {
      phrasalVerbDetail?.data?.score = "${soeModel?.data?.suggestedScore}";
      phrasalVerbDetail?.data?.userPronunciationUrl =
          audioUploadModel.data?.fileUrl;
    }

    notifyListeners();

    return true;
  }
}
