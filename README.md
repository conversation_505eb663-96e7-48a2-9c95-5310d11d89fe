# flutter_app_kouyu

A new Flutter project.

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.

# 环境安装
- 安装VSCode，chrome，xcode
- 在vscode上安装Flutter插件
- flutter version 
Flutter 3.19.0 • channel stable • https://github.com/flutter/flutter.git
Framework • revision bae5e49bc2 (12 days ago) • 2024-02-13 17:46:18 -0800
Engine • revision 04817c99c9
Tools • Dart 3.3.0 • DevTools 2.31.1
- flutter doctor检查fluter环境、设备

# vscode插件
- bloc bolc代码生成器
- dart 
- Flutter
- <PERSON><PERSON> to Dart Model json生成model代码
- Dart Data Class Generator 生成equal等方法


# 调试和运行
### 更新依赖库
```flutter pub get --no-example```
### 调试和运行
点击左侧导航栏 Run and Debug按钮，会默认在chrome浏览器运行项目

# 问题
### 检查不出来iOS模拟器
安装xcode
```
sudo xcode-select --switch /Applications/Xcode.app/Contents/Developer
sudo xcodebuild -runFirstLaunch
```

# 代码架构
## 目录结构
lib文件下创建了如下目录：
```
lib
├── common
├── l10n
├── modules
├── models
├── repository
├── routes
└── widgets 
```
文件夹	作用
common	一些工具类，如通用方法类、网络接口类、保存全局变量的静态类等
l10n	国际化相关的类都在此目录下
modules	模块
models	Json文件对应的Dart Model类会在此目录下
repository	配合bloc使用，提供数据
routes	存放所有路由页面类
widgets	APP内封装的一些Widget组件都在该目录下

#mac 出现问题
## Error (Xcode): ../../.pub-cache/hosted/pub.dev/win32-5.5.0/lib/src/guid.dart:32:9: Error: Type 'UnmodifiableUint8ListView' not found.
使用: flutter pub upgrade win32
flutter pub cache clean
不行就用
flutter pub upgrade

#mac 出现问题
Waiting for another flutter command to release the startup lock...

ps aux | grep flutter
kill 所有 flutter 进程
重新运行